import asyncio
import datetime
import itertools
import logging
import os
import time
from decimal import Decimal, getcontext
from statistics import mean
from typing import Any, Dict, List, Tuple, cast

import numpy as np
import pandas as pd
from async_timeout import timeout
from pydantic import Field

from hummingbot import data_path
from hummingbot.client.config.config_data_types import BaseClientModel, ClientFieldData
from hummingbot.connector.client_order_tracker import ClientOrderTracker
from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.connector.exchange_base import ExchangeBase
from hummingbot.connector.exchange_py_base import ExchangePyBase
from hummingbot.core.data_type.common import OrderType, PositionAction, PositionMode, PositionSide, TradeType
from hummingbot.core.data_type.in_flight_order import InFlightOrder, OrderState
from hummingbot.core.data_type.limit_order import LimitOrder
from hummingbot.core.data_type.order_candidate import OrderCandidate, PerpetualOrderCandidate
from hummingbot.core.event.event_forwarder import SourceInfoEventForwarder
from hummingbot.core.event.events import (
    BuyOrderCompletedEvent,
    BuyOrderCreatedEvent,
    LimitOrderStatus,
    MarketOrderFailureEvent,
    OrderBookEvent,
    OrderBookTradeEvent,
    OrderCancelledEvent,
    OrderFilledEvent,
    OrderType,
    SellOrderCompletedEvent,
    SellOrderCreatedEvent,
    TradeType,
)
from hummingbot.core.utils.async_utils import call_sync, safe_ensure_future, safe_gather
from hummingbot.smart_components.executors.position_executor.position_executor import PositionExecutor
from hummingbot.strategy.order_book_asset_price_delegate import OrderBookAssetPriceDelegate
from hummingbot.strategy.script_strategy_base import ScriptStrategyBase
from hummingbot.strategy.strategy_py_base import StrategyPyBase
from scripts.utils.utils import *

# import sys
# from pmm_scripts.path import path_to_pmm_scripts

# sys.path.append(path_to_pmm_scripts)
# import volatility_indicator  # must have same python version


NaN = float("nan")
s_decimal_zero = Decimal(0)
s_decimal_nan = Decimal("NaN")


class StinkBiddorConfig(BaseClientModel):
    exchange_stats: Dict[str, Any]
    script_file_name: str = Field(default_factory=lambda: os.path.basename(__file__))
    max_order_size_usd: Decimal
    volatility_to_spread_multiplier: Decimal


class StinkBiddor(ScriptStrategyBase, UtilsFunctions):
    """
    this is a version of xemm, that makes markets on multiple spot/futures markets and hedges on another spot/futures  markets
    """

    @classmethod
    def init_markets(cls, config: StinkBiddorConfig):
        markets = {}

        for ex, ex_dict in config.exchange_stats.items():
            if type(ex_dict["pair"]) == list:
                markets[ex] = set([i for i in ex_dict["pair"]])
            else:
                markets[ex] = {ex_dict["pair"]}
        cls.markets = markets

    def __init__(self, connectors: Dict[str, ConnectorBase], config: StinkBiddorConfig):
        super().__init__(connectors)
        self.config = config
        self.exchange_stats = self.config.exchange_stats
        self.volatility_to_spread_multiplier = Decimal(self.config.volatility_to_spread_multiplier)
        self._active_bids = {}
        self._active_asks = {}

        # convert floats to Decimal and fill active bids asks
        for ex, ex_dict in self.exchange_stats.items():
            self._active_bids[ex] = {}
            self._active_asks[ex] = {}

            for key, value in ex_dict.items():
                if isinstance(value, float):
                    ex_dict[key] = Decimal(str(value))

            for pair in self.markets[ex]:
                self._active_bids[ex][pair] = {}
                self._active_asks[ex][pair] = {}

                for level_idx, level in enumerate(self.exchange_stats[ex]["stink_levels"]):
                    self._active_bids[ex][pair][level_idx] = None
                    self._active_asks[ex][pair][level_idx] = None
                    for i, item in enumerate(level):
                        self.exchange_stats[ex]["stink_levels"][level_idx][i] = Decimal(str(item))

        self.status_ready = False
        self.is_perp = {}
        self.min_notional_size_dict = {}
        self.min_os_size_dict = {}
        self.min_price_step = {}
        self.on_tick_runtime = 0
        self.active_id_map = {}
        self.tp_order_id_to_active_order_id = {}

        # executor
        self.position_mode: PositionMode = PositionMode.ONEWAY
        #self.order_refresh_time = 30  # seconds
        self.position_time_limit = 30  # seconds
        self.market_making_strategy_name = "stink_biddor"
        self.set_leverage_flag = None
        self.leverage = 10
        self.active_executors: List[PositionExecutor] = []
        self.stored_executors: List[PositionExecutor] = []
        self.open_order_type = OrderType.LIMIT
        self.take_profit_order_type: OrderType = OrderType.LIMIT
        self.stop_loss_order_type: OrderType = OrderType.MARKET
        self.time_limit_order_type: OrderType = OrderType.MARKET
        self.volatility_indicator = {}
        self.fair_value = {}

    def on_tick(self):
        # set variables
        if not self.status_ready:
            # self.subscribe_to_orderbook_trade_event()

            for exchange, token in self.markets.items():
                safe_ensure_future(self.connectors[exchange]._update_balances())
                self.stop_tracking_all_orders(exchange)
                self.is_perp[exchange] = self.is_perpetual_exchange(exchange)
                self.volatility_indicator[exchange] = {}
                self.fair_value[exchange] = {}
                for pair in token:
                    self.is_perp[exchange] = self.is_perpetual_exchange(exchange)
                    if exchange not in self.min_notional_size_dict.keys():
                        self.min_notional_size_dict[exchange] = self.connectors[exchange].trading_rules[pair].min_notional_size
                    self.min_os_size_dict[exchange] = self.connectors[exchange].trading_rules[pair].min_order_size
                    self.min_price_step[exchange] = self.connectors[exchange].trading_rules[pair].min_price_increment
                    self.connectors[exchange].set_position_mode(self.position_mode)
                    self.connectors[exchange].set_leverage(trading_pair=pair, leverage=self.leverage)
                    self.fair_value[exchange][pair] = (self.connectors[exchange].get_price(pair, True) + self.connectors[exchange].get_price(pair, False)) / 2
                    self.volatility_indicator[exchange][pair] = VolatilityIndicator2(OrderBookAssetPriceDelegate(self.connectors[exchange], pair))

            self.status_ready = True

        # on tick
        start_time = time.perf_counter()
        # self.logger().info(f"self.volatility_indicator[].current_value: {self.volatility_indicator['gate_io_perpetual']['PEPE2-USDT'].current_value_pct}")
        self.check_orders()
        self.create_orders()

        self.on_tick_runtime = ((time.perf_counter() - start_time) * 1000)

    def stop_tracking_all_orders(self, exchange):
        active_orders = self.get_active_orders(exchange)
        all_order_ids = []
        for inflight_order in self.connectors[exchange].in_flight_orders.values():

            self.logger().info(f"delete: {inflight_order.client_order_id}")
            all_order_ids.append((inflight_order.trading_pair,inflight_order.client_order_id))

        for pair, id_ in all_order_ids:
            #self._sb_order_tracker.stop_tracking_limit_order(pair, id_)
            self.connectors[exchange].stop_tracking_order(id_)

    def is_above_min_order_size(self, exchange, amount, price):
        return True if self.min_notional_size_dict[exchange] < (amount * price) and self.min_os_size_dict[exchange] < amount else False

    def on_stop(self):
        """
        Without this functionality, the network iterator will continue running forever after stopping the strategy
        That's why is necessary to introduce this new feature to make a custom stop with the strategy.
        """
        for exchange, token in self.markets.items():
            # stop indicator threads
            for pair in token:
                try:
                    self.volatility_indicator[exchange][pair].stop()
                except KeyError:
                    pass
    def cancel_all_orders_on_exchange(self, exchange):
        for order in self.get_active_orders(connector_name=exchange):
            self.cancel(exchange, order.trading_pair, order.client_order_id)

    def check_orders(self):

        # check maker orders
        for exchange, token in self.markets.items():
            for pair in token:
                # update fair value
                old_fair_value = self.fair_value[exchange][pair]
                new_fair_value = (self.connectors[exchange].get_price(pair, True) + self.connectors[exchange].get_price(pair, False)) / 2

                # cancel if new fair value is beyond a certain threshold
                if not (new_fair_value * Decimal(1.0008)) >= old_fair_value >= (new_fair_value * Decimal(0.9992)):
                    self.fair_value[exchange][pair] = new_fair_value
                    self.cancel_all_orders_on_exchange(exchange)

        #check tp orders
        for tp_order_id, maker_order_id in self.tp_order_id_to_active_order_id.items():
            exchange = self.active_id_map[maker_order_id]["exchange"]
            inflight_order = self.connectors[exchange].in_flight_orders[tp_order_id]
            pair = inflight_order.trading_pair
            creation_timestamp = inflight_order.creation_timestamp
            if creation_timestamp + self.position_time_limit < self.current_timestamp:
                self.cancel(exchange, pair, tp_order_id)

    def create_orders(self):
        for exchange, token in self.markets.items():
            for pair in token:
                mid_price = self.connectors[exchange].get_mid_price(pair)

                for level, executor in self._active_asks[exchange][pair].items():

                    if executor is None:
                        EntryBPS = self.exchange_stats[exchange]["stink_levels"][level][0]
                        ExitBPS = self.exchange_stats[exchange]["stink_levels"][level][1]
                        QuoteSize = self.exchange_stats[exchange]["stink_levels"][level][2]

                        order_price = mid_price * (Decimal(1) + (EntryBPS / 10000) + (self.volatility_to_spread_multiplier * (self.volatility_indicator[exchange][pair].current_value_pct / 100)))
                        if self.is_perp[exchange]:
                            sell_order = PerpetualOrderCandidate(
                                trading_pair=pair,
                                is_maker=True,
                                order_type=OrderType.LIMIT,
                                order_side=TradeType.BUY,
                                amount=QuoteSize / mid_price,
                                price=order_price,
                            )
                        else:
                            sell_order = OrderCandidate(trading_pair=pair, is_maker=True, order_type=OrderType.LIMIT,
                                                        order_side=TradeType.BUY,
                                                        amount=QuoteSize / mid_price,
                                                        price=order_price)

                        sell_order_adjusted = self.connectors[exchange].budget_checker.adjust_candidate(sell_order, all_or_none=False)
                        is_above_min_os = self.is_above_min_order_size(exchange, sell_order_adjusted.amount, sell_order_adjusted.price)

                        # place order
                        if is_above_min_os:
                            order_id = self.sell(exchange, pair, sell_order_adjusted.amount, sell_order_adjusted.order_type, sell_order_adjusted.price)
                            if order_id:
                                self._active_asks[exchange][pair][level] = order_id
                                self.active_id_map[order_id] = {"exchange": exchange, "is_open": False, "tp_price": order_price * (Decimal(1) - (ExitBPS / 10000)), "tp_order_id": None}


                for level, executor in self._active_bids[exchange][pair].items():
                    if executor is None:
                        EntryBPS = self.exchange_stats[exchange]["stink_levels"][level][0]
                        ExitBPS = self.exchange_stats[exchange]["stink_levels"][level][1]
                        QuoteSize = self.exchange_stats[exchange]["stink_levels"][level][2]
                        order_price = mid_price * (Decimal(1) - (EntryBPS / 10000) - (self.volatility_to_spread_multiplier * (self.volatility_indicator[exchange][pair].current_value_pct / 100)))
                        if self.is_perp[exchange]:
                            buy_order = PerpetualOrderCandidate(
                                trading_pair=pair,
                                is_maker=True,
                                order_type=OrderType.LIMIT,
                                order_side=TradeType.BUY,
                                amount=QuoteSize / mid_price,
                                price=order_price,
                            )
                        else:
                            buy_order = OrderCandidate(trading_pair=pair, is_maker=True, order_type=OrderType.LIMIT,
                                                        order_side=TradeType.BUY,
                                                        amount=QuoteSize / mid_price,
                                                        price=order_price)

                        buy_order_adjusted = self.connectors[exchange].budget_checker.adjust_candidate(buy_order, all_or_none=False)
                        is_above_min_os = self.is_above_min_order_size(exchange, buy_order_adjusted.amount, buy_order_adjusted.price)

                        # place order
                        if is_above_min_os:
                            order_id = self.buy(exchange, pair, buy_order_adjusted.amount, buy_order_adjusted.order_type, buy_order_adjusted.price)
                            if order_id:
                                self._active_bids[exchange][pair][level] = order_id
                                self.active_id_map[order_id] = {"exchange": exchange, "is_open": False, "tp_price": order_price * (Decimal(1) + (ExitBPS / 10000))}

    def close_open_positions(self):
        # we are going to close all the open positions when the bot stops
        for connector_name, connector in self.connectors.items():
            for trading_pair, position in connector.account_positions.items():
                if position.position_side == PositionSide.LONG:
                    self.sell(connector_name=connector_name,
                              trading_pair=position.trading_pair,
                              amount=abs(position.amount),
                              order_type=OrderType.MARKET,
                              price=connector.get_mid_price(position.trading_pair),
                              position_action=PositionAction.CLOSE)
                elif position.position_side == PositionSide.SHORT:
                    self.buy(connector_name=connector_name,
                             trading_pair=position.trading_pair,
                             amount=abs(position.amount),
                             order_type=OrderType.MARKET,
                             price=connector.get_mid_price(position.trading_pair),
                             position_action=PositionAction.CLOSE)

    def format_status(self) -> str:
        if not self.ready_to_trade:
            return "Market connectors are not ready."
        lines = []
        warning_lines = []
        warning_lines.extend(self.network_warning(self.get_market_trading_pair_tuples()))

        volatility_df = self.get_volatility_df()
        lines.extend(
            ["", "  Volatility:"] + ["    " + line for line in volatility_df.to_string(index=False).split("\n")])

        lines.extend(["", f" on_tick_runtime: {self.on_tick_runtime:.4f} ms"])

        balance_df = self.get_balance_df()
        lines.extend(["", "  Balances:"] + ["    " + line for line in balance_df.to_string(index=False).split("\n")])

        return "\n".join(lines)

    def get_volatility_df(self):
        """
        Returns a data frame for all volatility for displaying purpose.
        """
        columns: List[str] = ["Exchange", "pair", "volatility", "spread", "mid", "vol_additional_spread"]
        data: List[Any] = []
        for connector_name, connector in self.connectors.items():
            for pair in self.markets[connector_name]:
                vol = self.volatility_indicator[connector_name][pair].current_value_pct
                spread = ((self.connectors[connector_name].get_price(pair, True) - self.connectors[connector_name].get_price(pair, False)) / self.connectors[connector_name].get_price(pair,
                                                                                                                                                                                       True)) * 100

                mid_price = self.connectors[connector_name].get_mid_price(pair)

                vol_additional_spread = vol

                data.append([connector_name,
                             pair,
                             f"{round(vol, 2)}%",
                             f"{round(spread, 2)}%",
                             f"{mid_price}",
                             f"{round(vol_additional_spread, 2)}%",
                             ])

        df = pd.DataFrame(data=data, columns=columns).replace(np.nan, '', regex=True)
        return df

    def did_fill_order(self, event: OrderFilledEvent):

        # handle entry orders
        if event.order_id in self.active_id_map.keys():
            amount = event.amount
            is_buy = True if event.trade_type == TradeType.BUY else False
            pair = event.trading_pair
            tp_price = self.active_id_map[event.order_id]["tp_price"]
            exchange = self.active_id_map[event.order_id]["exchange"]
            if is_buy:
                tp_order_id = self.sell(exchange, pair, amount,  OrderType.LIMIT, tp_price, position_action=PositionAction.CLOSE)
            else:
                tp_order_id = self.buy(exchange, pair, amount, OrderType.LIMIT, tp_price, position_action=PositionAction.CLOSE)

            self.active_id_map[event.order_id]["is_open"] = True
            self.tp_order_id_to_active_order_id[tp_order_id] = event.order_id


    def handle_order_complete_event(self, event):

        # handle if it is a tp order
        if event.order_id in self.tp_order_id_to_active_order_id.keys():
            maker_id = self.tp_order_id_to_active_order_id[event.order_id]
            self.tp_order_id_to_active_order_id.pop(event.order_id, None)

            for exchange, tokens in self.markets.items():
                for pair in tokens:
                    for level_entry in (self._active_bids[exchange][pair], self._active_asks[exchange][pair]):
                        for level, order_id in level_entry.items():
                            if order_id == maker_id:
                                level_entry[level] = None

    def did_complete_sell_order(self, event: SellOrderCompletedEvent):
        """
        Method called when the connector notifies a sell order has been completed (fully filled)
        """
        #
        # handle if its an entry order
        # place tp order
        #tp_price = self.active_id_map[event.order_id]["tp_price"]

        self.handle_order_complete_event(event)

    def did_complete_buy_order(self, event: BuyOrderCompletedEvent):
        """
        Method called when the connector notifies a buy order has been completed (fully filled)
        """
        #
        # handle if its an entry order
        # place tp orer
        #tp_price = self.active_id_map[event.order_id]["tp_price"]
        self.handle_order_complete_event(event)

    def did_cancel_order(self, event: OrderCancelledEvent):
        """
        Method called when the connector notifies an order has been cancelled
        """
        # handle if it is an entry order
        if event.order_id in self.active_id_map.keys():
            self.active_id_map.pop(event.order_id, None)

        #for exchange, token in self.markets.items():
        #    for pair in token:
        #        for level, order_id in self._active_bids[exchange][pair].items():
        #            if order_id == event.order_id:
        #                self._active_bids[exchange][pair][level] = None
        #        for level, order_id in self._active_asks[exchange][pair].items():
        #            if order_id == event.order_id:
        #                self._active_asks[exchange][pair][level] = None

            for exchange, tokens in self.markets.items():
                for pair in tokens:
                    for level_entry in (self._active_bids[exchange][pair], self._active_asks[exchange][pair]):
                        for level, order_id in level_entry.items():
                            if order_id == event.order_id:
                                level_entry[level] = None

        # handle if it is an exit order
        if event.order_id in self.tp_order_id_to_active_order_id.keys():
            tp_order_id = event.order_id
            maker_order_id = self.tp_order_id_to_active_order_id[tp_order_id]
            # place Taker order
            exchange = self.active_id_map[maker_order_id]["exchange"]
            inflight_order = self.connectors[exchange].in_flight_orders[tp_order_id]
            pair = inflight_order.trading_pair
            amount = inflight_order.amount - inflight_order.executed_amount_base
            is_buy = True if inflight_order.trade_type == TradeType.BUY else False
            if is_buy:
                new_tp_order = self.buy(exchange, pair, amount, OrderType.MARKET, position_action=PositionAction.CLOSE)
            else:
                new_tp_order = self.sell(exchange, pair, amount, OrderType.MARKET, position_action=PositionAction.CLOSE)

            self.tp_order_id_to_active_order_id[new_tp_order] = maker_order_id
            self.tp_order_id_to_active_order_id.pop(event.order_id, None)

    def did_fail_order(self, event: MarketOrderFailureEvent):
        """
        Method called when the connector notifies an order has failed
        """
        # handle if it's an entry order
        if event.order_id in self.active_id_map.keys():
            self.active_id_map.pop(event.order_id, None)

        for exchange, token in self.markets.items():
            for pair in token:
                for level, order_id in self._active_bids[exchange][pair].items():
                    if order_id == event.order_id:
                        self._active_bids[exchange][pair][level] = None
                for level, order_id in self._active_asks[exchange][pair].items():
                    if order_id == event.order_id:
                        self._active_asks[exchange][pair][level] = None

        # handle if its an exit order


