#!/usr/bin/env python3
import sqlite3
import csv
import sys
import pandas as pd

def export_csv(db_file):

    output_csv = db_file.strip(".sqlite") + "_trade_fill.csv"
    # Establish a connection to the database
    conn = sqlite3.connect(db_file)

    query = "SELECT * FROM TradeFill"

    # Read the data into a DataFrame
    df = pd.read_sql(query, conn)

    conn.close()

    print(f"len df: {len(df)}")


    # Write rows to the CSV file
    print(f"write file to {output_csv}")
    # Save the DataFrame to a CSV file without the index column
    df.to_csv(output_csv, index=False)

    # Close the database connection
    conn.close()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python export_tradefill.py conf_WOD.sqlite ")
        sys.exit(1)
    
    db_file = sys.argv[1]
    export_csv(db_file)