import asyncio
import logging
from typing import Optional

import ccxt.pro as ccxtpro
import numpy as np

from hummingbot.core.api_throttler.data_types import RateLimit
from hummingbot.core.network_iterator import NetworkStatus, safe_ensure_future
from hummingbot.core.web_assistant.ws_assistant import WSAssistant
from hummingbot.data_feed.candles_feed.candles_base import CandlesBase

# from hummingbot.data_feed.candles_feed.ccxt_candles import constants as CONSTANTS
from hummingbot.data_feed.candles_feed.ccxt_candles.ohlcv_builder import OhlcvBuilder
from hummingbot.logger import HummingbotLogger

np.set_printoptions(suppress=True, precision=10)


class CcxtCandles(CandlesBase):
    _logger: Optional[HummingbotLogger] = None

    @classmethod
    def logger(cls) -> HummingbotLogger:
        if cls._logger is None:
            cls._logger = logging.getLogger(__name__)
        return cls._logger

    def get_exchange_ccxt(self, exchange_name, config):
        exchange_class = getattr(ccxtpro, exchange_name)
        return exchange_class(config)

    def __init__(self, trading_pair: str, interval: str = "1m", max_records: int = 150, exchange_name: str = "binance"):
        # check if the market exists
        exchange_names = ccxtpro.exchanges
        cur_exchange_name = exchange_name.lower().replace("_", "")
        self._exchange_name = exchange_name
        if cur_exchange_name not in exchange_names:
            raise ValueError(f"Market {self._exchange_name} is not supported by ccxtpro, please use a different market of the following: {exchange_names}")

        self._ccxtpro_exchange = self.get_exchange_ccxt(cur_exchange_name, {})
        self._test_done = False
        self._ohlcv_builder = None
        super().__init__(trading_pair, interval, max_records)

    @property
    def name(self):
        return f"ccxtpro_{self._exchange_name}_{self._trading_pair}"

    # @property
    # def rest_url(self):
    #     return None

    # @property
    # def wss_url(self):
    #     return None

    # @property
    # def health_check_url(self):
    #     return None

    # @property
    # def candles_url(self):
    #     return None

    @property
    def rate_limits(self):
        # not a real rate limit, just a placeholder
        return [RateLimit(limit_id="123", limit=1, time_interval=1)]

    # @property
    # def intervals(self):
    #     return CONSTANTS.INTERVALS

    def save_to_csv(self, save_path=None):
        self.candles_df.to_csv("ccxt_candles.csv" if save_path is None else save_path)

    async def listen_for_subscriptions(self):
        """
        Connects to the candlestick websocket endpoint and listens to the messages sent by the
        exchange.
        """
        while True:
            try:
                await self._process_websocket_messages()
            except asyncio.CancelledError:
                raise
            except ConnectionError as connection_exception:
                self.logger().warning(f"The websocket connection was closed ({connection_exception})")
            except Exception:
                self.logger().exception(
                    "Unexpected error occurred when listening to public klines. Retrying in 1 seconds...",
                )
                await self._sleep(1.0)
            finally:
                self._candles.clear()

    def get_exchange_trading_pair(self, trading_pair):
        return trading_pair.replace("-", "/")

    async def fetch_candles(self,
                            start_time: Optional[int] = None,
                            end_time: Optional[int] = None,
                            limit: Optional[int] = 500):

        candles = await self._ccxtpro_exchange.fetch_ohlcv(self._ex_trading_pair, self.interval, start_time, limit)

        new_hb_candles = []
        for i in candles:
            timestamp_ms = i[0]
            open = i[1]
            high = i[2]
            low = i[3]
            close = i[4]
            volume = i[5]
            # no data field
            quote_asset_volume = 0
            n_trades = 0
            taker_buy_base_volume = 0
            taker_buy_quote_volume = 0
            new_hb_candles.append([timestamp_ms, open, high, low, close, volume,
                                   quote_asset_volume, n_trades, taker_buy_base_volume,
                                   taker_buy_quote_volume])
        return np.array(new_hb_candles).astype(float)

    async def fill_historical_candles(self):

        limit = min(500, self._candles.maxlen - len(self._candles))
        try:
            end_timestamp = int(self._candles[0][0]) if len(self._candles) > 0 else OhlcvBuilder.round_to_nearest_timestamp_ms(self.interval)
            start_time = end_timestamp - (limit * 60 * 1000)
            candles = await self.fetch_candles(start_time=start_time, limit=limit + 1)

            start_fetched_candles = self._candles.maxlen - len(self._candles) + 1
            self._candles.extendleft(candles[-(start_fetched_candles + 1):-1][::-1])
            if self._candles[-1][0] == self._candles[-2][0]:
                self._candles.pop()

                raise
        except asyncio.CancelledError:
            raise
        except Exception:
            self.logger().exception(
                "Unexpected error occurred when getting historical klines. Retrying in 1 seconds...",
            )

    async def _subscribe_channels(self, ws: WSAssistant):
        """
        Subscribes to the candles events through the provided websocket connection.
        :param ws: the websocket assistant used to connect to the exchange
        """
        pass

    async def _process_websocket_messages(self):

        while True:
            try:
                trades = await self._ccxtpro_exchange.watch_trades(self.get_exchange_trading_pair(self._trading_pair))

                if self._ohlcv_builder:
                    data = self._ohlcv_builder.build_ohlcv(trades)

                    timestamp_ms = data["timestamp"]
                    open = data["open"]
                    high = data["high"]
                    low = data["low"]
                    close = data["close"]
                    volume = data["volume"]
                    # no data field
                    quote_asset_volume = 0
                    n_trades = 0
                    taker_buy_base_volume = 0
                    taker_buy_quote_volume = 0

                    if timestamp_ms > int(self._candles[-1][0]):
                        self._candles.append(np.array([timestamp_ms, open, high, low, close, volume,
                                                       quote_asset_volume, n_trades, taker_buy_base_volume,
                                                       taker_buy_quote_volume]))
                    elif timestamp_ms == int(self._candles[-1][0]):
                        self._candles.pop()
                        self._candles.append(np.array([timestamp_ms, open, high, low, close, volume,
                                                       quote_asset_volume, n_trades, taker_buy_base_volume,
                                                       taker_buy_quote_volume]))

                if len(self._candles) == 0:
                    # this needs to be here and not before watch_trades, because it will freeze the application
                    while len(self._candles) < self._candles.maxlen:
                        await self.fill_historical_candles()
                    self._ohlcv_builder = OhlcvBuilder(self.interval, self._candles[-1][1], self._candles[-1][2], self._candles[-1][3], self._candles[-1][4], self._candles[-1][5])

            except Exception as e:
                self.logger().exception(f"Error fetching historical candles: {str(e)}")

    def start(self):
        self._check_network_task = None
        self._network_status = NetworkStatus.NOT_CONNECTED
        self._started = True
        safe_ensure_future(self.start_network())
