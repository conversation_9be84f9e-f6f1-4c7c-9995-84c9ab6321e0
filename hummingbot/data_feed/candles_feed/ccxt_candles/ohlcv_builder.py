import time

from hummingbot.connector.exchange_base import bidict

interval_to_seconds = bidict({
    "1s": 1,
    "1m": 60,
    "3m": 180,
    "5m": 300,
    "15m": 900,
    "30m": 1800,
    "1h": 3600,
    "2h": 7200,
    "4h": 14400,
    "6h": 21600,
    "8h": 28800,
    "12h": 43200,
    "1d": 86400,
    "3d": 259200,
    "1w": 604800,
    "1M": 2592000
})


class OhlcvBuilder:

    def __init__(self, interval, open_price, high_price, low_price, close_price, volume):
        self.interval = interval
        self._next_candle_timestamp = None
        self._current_candle_timestamp = None
        self._open = open_price
        self._high = high_price
        self._low = low_price
        self._close = close_price
        self._volume = volume

        self.set_current_candle_timestamp()
        self.set_next_candle_timestamp()

    def set_open(self, open_price):
        self._open = open_price

    def set_high(self, high_price):
        self._high = high_price

    def set_low(self, low_price):
        self._low = low_price

    def set_close(self, close_price):
        self._close = close_price

    def set_volume(self, volume):
        self._volume = volume

    def set_current_candle_timestamp(self):
        self._current_candle_timestamp = self.round_to_nearest_timestamp_ms(self.interval)

    @staticmethod
    def round_to_nearest_timestamp_ms(interval):
        current_timestamp_ms = int(time.time() * 1000)
        interval_ms = interval_to_seconds[interval] * 1000

        # Round down the current timestamp to the nearest interval
        rounded_timestamp_ms = (current_timestamp_ms // interval_ms) * interval_ms

        return rounded_timestamp_ms

    def set_next_candle_timestamp(self):
        self._next_candle_timestamp = self.get_next_timestamp_ms(self.interval)

    def get_next_timestamp_ms(self, interval):
        return self.round_to_nearest_timestamp_ms(interval) + interval_to_seconds[interval] * 1000

    def update_ohlcv(self, timestamp_ms, price, volume):
        """
        this method is used to build the ohlcv data from the trades data coming from ccxt
        """
        if timestamp_ms > self._next_candle_timestamp:
            self.set_current_candle_timestamp()
            self.set_next_candle_timestamp()
            self.set_open(price)
            self.set_high(price)
            self.set_low(price)
            self.set_close(price)
            self.set_volume(volume)
        else:
            if self._high is None or price > self._high:
                self.set_high(price)
            if self._low is None or price < self._low:
                self.set_low(price)
            self.set_close(price)
            self.set_volume(self._volume + volume)

    def build_ohlcv(self, trades):
        for trade in trades:
            self.update_ohlcv(trade["timestamp"], trade["price"], trade["amount"])

        return {
            "timestamp": self._current_candle_timestamp,
            "open": self._open,
            "high": self._high,
            "low": self._low,
            "close": self._close,
            "volume": self._volume
        }
