import asyncio
import csv
import os
import time
from dataclasses import dataclass, asdict
from typing import Dict, Optional
from threading import Lock
from decimal import Decimal


@dataclass
class LatencyRecord:
    """Data class to store latency tracking information for an order"""
    timestamp: float
    order_id: str
    exchange: str
    trading_pair: str
    side: str
    order_type: str
    price: Optional[Decimal]
    amount: Decimal
    order_id_generation_time: float
    pre_api_time: Optional[float] = None
    api_execution_time: Optional[float] = None
    confirmation_time: Optional[float] = None
    
    @property
    def order_id_to_pre_api_ms(self) -> Optional[float]:
        """Time from order ID generation to pre-API in milliseconds"""
        if self.pre_api_time is None:
            return None
        return (self.pre_api_time - self.order_id_generation_time) * 1000
    
    @property
    def pre_api_to_execution_ms(self) -> Optional[float]:
        """Time from pre-API to API execution in milliseconds"""
        if self.pre_api_time is None or self.api_execution_time is None:
            return None
        return (self.api_execution_time - self.pre_api_time) * 1000
    
    @property
    def api_execution_to_confirmation_ms(self) -> Optional[float]:
        """Time from API execution to confirmation in milliseconds"""
        if self.api_execution_time is None or self.confirmation_time is None:
            return None
        return (self.confirmation_time - self.api_execution_time) * 1000
    
    @property
    def order_id_to_api_execution_ms(self) -> Optional[float]:
        """Time from order ID generation to API execution in milliseconds"""
        if self.api_execution_time is None:
            return None
        return (self.api_execution_time - self.order_id_generation_time) * 1000
    
    @property
    def total_latency_ms(self) -> Optional[float]:
        """Total time from order ID generation to confirmation in milliseconds"""
        if self.confirmation_time is None:
            return None
        return (self.confirmation_time - self.order_id_generation_time) * 1000


class LatencyTracker:
    """
    Tracks latency from order ID generation to API execution and confirmation.
    Thread-safe singleton implementation.
    """
    
    _instance = None
    _lock = Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._records: Dict[str, LatencyRecord] = {}
        self._file_lock = Lock()
        self._csv_file_path = "order_latency_tracking.csv"
        self._ensure_csv_headers()
    
    def _ensure_csv_headers(self):
        """Ensure CSV file exists with proper headers"""
        if not os.path.exists(self._csv_file_path):
            with open(self._csv_file_path, 'w', newline='') as csvfile:
                fieldnames = [
                    'timestamp', 'order_id', 'exchange', 'trading_pair', 'side', 
                    'order_type', 'price', 'amount', 'order_id_generation_time',
                    'pre_api_time', 'api_execution_time', 'confirmation_time',
                    'order_id_to_pre_api_ms', 'pre_api_to_execution_ms', 
                    'api_execution_to_confirmation_ms', 'order_id_to_api_execution_ms',
                    'total_latency_ms'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
    
    def start_tracking_order(self, order_id: str, exchange: str, trading_pair: str, 
                           side: str, order_type: str, price: Optional[Decimal], 
                           amount: Decimal) -> None:
        """Start tracking latency for a new order"""
        current_time = time.time()
        
        record = LatencyRecord(
            timestamp=current_time,
            order_id=order_id,
            exchange=exchange,
            trading_pair=trading_pair,
            side=side,
            order_type=order_type,
            price=price,
            amount=amount,
            order_id_generation_time=current_time
        )
        
        with self._lock:
            self._records[order_id] = record
    
    def mark_pre_api(self, order_id: str) -> None:
        """Mark the time just before API execution"""
        with self._lock:
            if order_id in self._records:
                self._records[order_id].pre_api_time = time.time()
    
    def mark_api_execution(self, order_id: str) -> None:
        """Mark the time when API execution completes"""
        with self._lock:
            if order_id in self._records:
                self._records[order_id].api_execution_time = time.time()
    
    def mark_confirmation(self, order_id: str) -> None:
        """Mark the time when order confirmation is received and save to CSV"""
        with self._lock:
            if order_id in self._records:
                self._records[order_id].confirmation_time = time.time()
                self._save_record_to_csv(self._records[order_id])
                # Remove from memory after saving to prevent memory leaks
                del self._records[order_id]
    
    def _save_record_to_csv(self, record: LatencyRecord) -> None:
        """Save a completed latency record to CSV file"""
        with self._file_lock:
            try:
                with open(self._csv_file_path, 'a', newline='') as csvfile:
                    fieldnames = [
                        'timestamp', 'order_id', 'exchange', 'trading_pair', 'side', 
                        'order_type', 'price', 'amount', 'order_id_generation_time',
                        'pre_api_time', 'api_execution_time', 'confirmation_time',
                        'order_id_to_pre_api_ms', 'pre_api_to_execution_ms', 
                        'api_execution_to_confirmation_ms', 'order_id_to_api_execution_ms',
                        'total_latency_ms'
                    ]
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    # Convert record to dict and add calculated fields
                    row_data = asdict(record)
                    row_data['order_id_to_pre_api_ms'] = record.order_id_to_pre_api_ms
                    row_data['pre_api_to_execution_ms'] = record.pre_api_to_execution_ms
                    row_data['api_execution_to_confirmation_ms'] = record.api_execution_to_confirmation_ms
                    row_data['order_id_to_api_execution_ms'] = record.order_id_to_api_execution_ms
                    row_data['total_latency_ms'] = record.total_latency_ms
                    
                    writer.writerow(row_data)
            except Exception as e:
                print(f"Error saving latency record to CSV: {e}")
    
    def get_active_tracking_count(self) -> int:
        """Get the number of orders currently being tracked"""
        with self._lock:
            return len(self._records)
    
    def cleanup_stale_records(self, max_age_seconds: float = 300) -> None:
        """Remove records older than max_age_seconds to prevent memory leaks"""
        current_time = time.time()
        with self._lock:
            stale_order_ids = [
                order_id for order_id, record in self._records.items()
                if current_time - record.order_id_generation_time > max_age_seconds
            ]
            for order_id in stale_order_ids:
                del self._records[order_id]


# Global instance
latency_tracker = LatencyTracker()
