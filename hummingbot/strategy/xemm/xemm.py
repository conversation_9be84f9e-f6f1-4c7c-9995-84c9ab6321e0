import asyncio
import copy
import logging
import time
import traceback
from collections import deque
from decimal import Decimal
from functools import partial
from typing import Any, Awaitable, Deque, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
import urllib3
from async_timeout import timeout

from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.core.clock import Clock
from hummingbot.core.data_type.common import PositionAction, PositionSide
from hummingbot.core.data_type.in_flight_order import OrderState
from hummingbot.core.data_type.limit_order import LimitOrder
from hummingbot.core.data_type.order_candidate import OrderCandidate, PerpetualOrderCandidate
from hummingbot.core.event.event_forwarder import SourceInfoEventForwarder
from hummingbot.core.event.events import (
    BuyO<PERSON><PERSON><PERSON>ompletedEvent,
    BuyOrderCreatedEvent,
    FundingPaymentCompletedEvent,
    MarketOrderFailureEvent,
    OrderBookEvent,
    OrderBookTradeEvent,
    OrderCancelledEvent,
    OrderFilledEvent,
    OrderType,
    SellO<PERSON><PERSON><PERSON>ompletedEvent,
    Sell<PERSON><PERSON>r<PERSON>reated<PERSON><PERSON>,
    TradeType,
)
from hummingbot.core.utils.async_utils import safe_ensure_future, safe_gather
from hummingbot.strategy.data_collector.data_collector import <PERSON>Collector
from hummingbot.strategy.Indicators.volatility_indicator import VolatilityIndicator
from hummingbot.strategy.market_trading_pair_tuple import MarketTradingPairTuple
from hummingbot.strategy.strategy_py_base import StrategyPyBase
from hummingbot.strategy.xemm.auto_buy_sell_inventory import AutoBuySellInventory
from hummingbot.strategy.xemm.data_types import XEMMArbProposal, XEMMTrade
from hummingbot.strategy.xemm.utils import SoftDeleteDict
from hummingbot.strategy.xemm.xemm_reporter import XEMMReporter

NaN = float("nan")
s_decimal_zero = Decimal(0)
s_decimal_nan = Decimal("NaN")
s_float_nan = float("nan")
s_decimal_1 = Decimal("1")
s_decimal_0 = Decimal("0")
s_logger = None


class XEMMStrategy(StrategyPyBase):
    """
    this is a version of XEMM, that makes markets on multiple spot/futures markets and hedges on another spot/futures  market.

    version: 1.4
    """

    @classmethod
    def logger(cls):
        global s_logger
        if s_logger is None:
            s_logger = logging.getLogger(__name__)
        return s_logger

    def init_params(self,
                    exchange_stats: Dict[str, Dict],
                    connectors: Dict,
                    max_order_size_quote: Decimal,
                    volatility_to_spread_multiplier: Decimal,
                    idle_amount_in_quote: Decimal,
                    mode: str,
                    market_making_settings: Dict,
                    profit_settings: Dict,
                    report_to_dbs: bool,
                    hedge_order_slippage_tolerance: Decimal,
                    bucket: str,
                    interval: Decimal,
                    bot_identifier: Decimal,
                    monitor_open_order_data: bool,
                    monitor_balance_data: bool,
                    monitor_market_data: bool,
                    taker_order_type: str,
                    auto_buy_sell_inventory_base_amount: Decimal,
                    collect_data: bool,
                    calculate_on_ob_diff: bool,
                    use_public_trades_for_fill_information: bool = False,

                    ):
        self.exchange_stats: Dict[str, Dict] = exchange_stats
        self.connectors: Dict = connectors
        self.mode: str = mode
        self.market_making_settings: Dict = market_making_settings
        self.profit_settings: Dict = profit_settings
        self.report_to_dbs: bool = report_to_dbs
        self.hedge_order_slippage_tolerance: Decimal = hedge_order_slippage_tolerance
        self.bucke: str = bucket
        self.interval: Decimal = interval
        self.bot_identifier: Decimal = bot_identifier
        self.monitor_open_order_data: bool = monitor_open_order_data
        self.monitor_balance_data: bool = monitor_balance_data
        self.monitor_market_data: bool = monitor_market_data
        self.markets: Dict[str, str] = {ex: {stats["pair"]} for ex, stats in self.exchange_stats.items()}
        self.max_order_size_quote: Decimal = max_order_size_quote  # amount in USD for each order
        self.volatility_to_spread_multiplier: Decimal = volatility_to_spread_multiplier
        self.idle_amount_in_quote: Decimal = idle_amount_in_quote
        self.taker_order_type: OrderType = OrderType.MARKET if taker_order_type == "market" else OrderType.LIMIT
        self.auto_buy_sell_inventory_base_amount: Decimal = Decimal(auto_buy_sell_inventory_base_amount)
        self.calculate_on_ob_diff: bool = calculate_on_ob_diff
        self.min_profit_dict: Dict = {}
        self.max_order_age_seconds: int = 60 * 30  # 30 minutes
        self.min_notional_size_dict: Dict = {}  # "kucoin": 1 # custom min_notational. leave blank if not required
        self.last_recorded_mid_prices: Dict = {}
        self.use_public_trades_for_fill_information: bool = use_public_trades_for_fill_information
        self.reporter: Optional[XEMMReporter] = None
        self.auto_buy_sell_inventory: Optional[AutoBuySellInventory] = None
        self.collect_data: bool = collect_data

        # convert floats to Decimal
        for ex, ex_dict in self.exchange_stats.items():
            for key, value in ex_dict.items():
                if isinstance(value, float):
                    ex_dict[key] = Decimal(str(value))
        for ex, ex_dict in self.profit_settings.items():
            self.min_profit_dict[ex] = Decimal(ex_dict["min_profit"])

        self.status_ready: bool = False
        self._all_markets_ready: bool = False
        self.exchange_info: Dict = {}
        self.hedge_trades: Dict = {}  # {"exchange_trade_id": {"is_buy": True, "amount": Decimal(100),"maker_exchange": maker_exchange, "status": "in_process", "event": event}}
        self.hedge_order_id_to_filled_maker_exchange_trade_id: Dict = {}  # {"hedge_order_id": "filled_maker_order_id"}
        self.maker_order_id_to_hedge_exchange: Dict = {}  # {"maker_order_id": ("hedge_exchange", "hedge_price")}
        self.ids_to_cancel = set()
        self.min_price_step: Dict = {}
        self.time_out_dict: Dict = {}
        self.optimal_quotes: Dict = {}
        self.is_perp: Dict = {}
        self.min_os_size_dict: Dict = {}
        self.volatility_indicator: Dict = {}
        self.orderbook_indicator: Dict = {}
        self.trades_indicator: Dict = {}
        self.order_id_creation_timestamp: List = []
        self.latency_roundtrip: Dict = {}
        self.order_latency: Dict = {}  # order_id: latency
        self.order_creation_events: Dict = {}  # this is used to await pending create orders in order to properly cancel them
        self._base_asset_amount: Deque[float] = deque(maxlen=3)
        self._base_asset_amount_last_checked: int = 0
        self.order_book_diff_events: Dict = {}
        self.order_cancel_events: Dict = {}
        self.ob_update_event_timestamps: Deque = deque(maxlen=1000)
        self.data_collector = None
        self.main_tick_logic_running: bool = False
        self._order_book_trade_events: Dict = {}
        self._maker_order_dict: SoftDeleteDict = SoftDeleteDict(1000)  # order id to XEMMArbProposal
        self._taker_order_dict: SoftDeleteDict = SoftDeleteDict(1000)  # order id to XEMMTrade
        self._global_unhedged_position: Decimal = Decimal(0)
        self._all_trading_pair_tuples_list: List[MarketTradingPairTuple] = []
        self._await_cancelation_exchanges_set: set[str] = {"ascend_ex", "mexc"}
        for exchange, trading_pairs in self.markets.items():
            for trading_pair in trading_pairs:
                self._all_trading_pair_tuples_list.append(MarketTradingPairTuple(self.connectors[exchange], trading_pair, trading_pair.split("-")[0], trading_pair.split("-")[1]))

        self.add_markets([self.connectors[ex] for ex in self.markets.keys()])

        # handle kucoin hft specific case
        if "kucoin_hft" in self.exchange_stats:
            self.exchange_stats["kucoin"] = self.exchange_stats.pop("kucoin_hft")

        if "kucoin_hft" in self.exchange_info:
            self.exchange_info["kucoin"] = self.exchange_info.pop("kucoin_hft")

        if "kucoin_hft" in self.profit_settings:
            self.profit_settings["kucoin"] = self.profit_settings.pop("kucoin_hft")

        if "kucoin_hft" in self.min_profit_dict:
            self.min_profit_dict["kucoin"] = self.min_profit_dict.pop("kucoin_hft")

    @property
    def all_trading_pair_tuples(self) -> list:
        return self._all_trading_pair_tuples_list

    @property
    def active_maker_limit_orders(self):
        return [(ex, order, order.client_order_id) for ex, order in self._sb_order_tracker.active_limit_orders if self._maker_order_dict.has_live(order.client_order_id)]

    @property
    def active_limit_orders(self):
        return [(ex, order, order.client_order_id) for ex, order in self._sb_order_tracker.active_limit_orders]

    def get_active_limit_order_by_id(self, client_order_id: str) -> LimitOrder:
        for ex, order, order_id in self.active_limit_orders:
            if order_id == client_order_id:
                return order

        raise ValueError(f"Limit order with client_order_id {client_order_id} not found.")

    def set_variables(self) -> None:
        """Initialize all strategy subsystems and mark ready."""
        if self.use_public_trades_for_fill_information:
            self.add_listeners()

        if self.calculate_on_ob_diff:
            self.subscribe_to_order_book_update_event()

        if self.auto_buy_sell_inventory_base_amount != Decimal(0):
            self.init_auto_inventory()

        if self.report_to_dbs:
            self.start_reporter()

        if self.collect_data:
            self.start_data_collector()

        self.init_markets()
        self.status_ready = True

    def init_auto_inventory(self) -> None:
        """Configure and kick off auto buy/sell inventory logic."""
        asset_tuple = self.all_trading_pair_tuples[0]
        asset = asset_tuple.base_asset
        amount_in_quote = (
                self.auto_buy_sell_inventory_base_amount / asset_tuple.get_mid_price()
        )
        inv = AutoBuySellInventory(
            strategy=self,
            market_info_list=self.all_trading_pair_tuples,
            amount=amount_in_quote,
            asset=asset,
        )
        self.auto_buy_sell_inventory = inv
        inv.buy_inventory()

    def start_reporter(self) -> None:
        """Instantiate and start the DB reporter."""
        self.reporter = XEMMReporter(
            sb_order_tracker=self._sb_order_tracker,
            market_pairs=self.all_trading_pair_tuples,
            bot_identifier=self.bot_identifier,
            monitor_market_data=self.monitor_market_data,
            monitor_balance_data=self.monitor_balance_data,
            monitor_open_order_data=self.monitor_open_order_data,
            asset_price_delegate=None,
            bucket=self.bucket,
            interval=int(self.interval),
        )
        self.reporter.start()

    def start_data_collector(self) -> None:
        """Instantiate and start the data collector."""
        self.data_collector = DataCollector(
            market_infos=self.all_trading_pair_tuples,
            depth=20,
            snapshot_on_orderbook_diff=False,
            snapshot_interval_s=0.1,
            time_between_csv_dumps_s=100,
        )
        self.data_collector.start()

    def init_markets(self) -> None:
        """Set up per-exchange structures and indicators."""
        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            base, quote = pair.split("-")

            self.time_out_dict[exchange] = False
            self.is_perp[exchange] = self.is_perpetual_exchange(exchange)

            rules = market_info.market.trading_rules[pair]
            self.min_notional_size_dict.setdefault(exchange, rules.min_notional_size)
            self.min_os_size_dict[exchange] = rules.min_order_size
            self.min_price_step[exchange] = rules.min_price_increment
            self.latency_roundtrip[exchange] = deque(maxlen=10)

            mttp = MarketTradingPairTuple(
                market=market_info.market,
                trading_pair=pair,
                base_asset=base,
                quote_asset=quote,
            )
            # prepare empty dicts and attach indicators
            for container in (self.volatility_indicator, self.orderbook_indicator, self.trades_indicator):
                container.setdefault(exchange, {})[pair] = VolatilityIndicator(
                    market=mttp,
                    main_loop_update_interval_s=1
                )

    def main_tick_logic(self) -> None:

        if not self.main_tick_logic_running:
            start_time = time.perf_counter()
            self.main_tick_logic_running = True

            try:
                # check quotes and calculate balances
                self.check_balances_and_quotes()

                # calculate optimal quotes
                self.calculate_optimal_quotes()

                # place orders
                self.place_orders()

                # check base asset drift
                if self.current_timestamp > self._base_asset_amount_last_checked + 10:
                    self.check_for_base_asset_drift()

            except Exception:
                error_message = traceback.format_exc()
                self.logger().error(f"Error in main_tick_logic:\n{error_message}")

            self.record_last_recorded_mid_prices()
            self.on_tick_runtime = ((time.perf_counter() - start_time) * 1000)
            self.main_tick_logic_running = False

    def tick(self, timestamp: float):

        if not self._all_markets_ready:
            self._all_markets_ready = all([market.ready for market in self.active_markets])
            if not self._all_markets_ready:
                self.logger().warning("Markets are not ready. No market making trades are permitted.")
                return
            else:
                self.logger().info("Markets are ready.")

        if not self.status_ready:
            self.set_variables()

        if not self.calculate_on_ob_diff:
            self.main_tick_logic()

    def record_last_recorded_mid_prices(self):
        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            mid_price = market_info.market.get_mid_price(pair)
            self.last_recorded_mid_prices[exchange] = mid_price

    def start(self, clock: Clock, timestamp: float):
        super().start(clock, timestamp)
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def check_for_base_asset_drift(self):
        def is_change_within_threshold(value1, value2, threshold=0.5):
            if value2 == Decimal(0) and value1 != value2:
                return False
            elif value2 == Decimal(0) and value1 == value2:
                return True
            else:
                return abs(value1 - value2) / value2 * 100 <= threshold

        self._base_asset_amount_last_checked = self.current_timestamp
        base_amount = 0
        for market_info in self._all_trading_pair_tuples_list:
            pair = market_info.trading_pair
            base, quote = pair.split("-")
            base_amount += market_info.market.get_balance(base)

        # add on going hedges
        for exchange_trade_id, hedge_trade_dict in self.hedge_trades.items():
            if hedge_trade_dict["status"] in ["in_process", "failed"]:
                base_amount += hedge_trade_dict["amount"] if hedge_trade_dict["is_buy"] else -hedge_trade_dict["amount"]

        if not len(self._base_asset_amount) == 3:
            self._base_asset_amount.append(base_amount)

        elif not is_change_within_threshold(self._base_asset_amount[0], self._base_asset_amount[2]) and not is_change_within_threshold(self._base_asset_amount[1], self._base_asset_amount[2]):
            msg = f"Base asset drift detected: {self._base_asset_amount[0]} -> {self._base_asset_amount[2]}"
            self.logger().info(msg)
            self.notify_hb_app_with_timestamp(msg)
        self._base_asset_amount.append(base_amount)

    def buy(self,
            market_info: MarketTradingPairTuple,
            amount: Decimal,
            order_type: OrderType,
            price=s_decimal_nan,
            position_action=PositionAction.OPEN) -> str:

        self.logger().info(f"Creating {market_info.trading_pair} buy order: price: {price} amount: {amount}.")
        return self.buy_with_specific_market(market_info, amount, order_type, price, position_action=position_action)

    def sell(self,
             market_info: MarketTradingPairTuple,
             amount: Decimal,
             order_type: OrderType,
             price=s_decimal_nan,
             position_action=PositionAction.OPEN) -> str:

        self.logger().info(f"Creating {market_info.trading_pair} sell order: price: {price} amount: {amount}.")
        return self.sell_with_specific_market(market_info, amount, order_type, price, position_action=position_action)

    def cancel(self,
               market_info: MarketTradingPairTuple,
               order_id: str):
        self.cancel_order(market_trading_pair_tuple=market_info, order_id=order_id)

    def is_valid_id(self, id):
        for (exchange, order, order_id) in self.active_limit_orders:
            if order.client_order_id == id:
                return True
        return False

    def place_orders(self) -> None:
        """
        Iterate through trading pairs and place both buy and sell orders
        from optimal_quotes, skipping when cancellation is pending or
        when the side is not active.
        """

        # Skip placing if there are orders pending cancellation
        if self.ids_to_cancel:
            return

        for market_info in self.all_trading_pair_tuples:
            exchange = market_info.market.name
            # Skip if exchange is timed out or not in maker mode
            if self.time_out_dict.get(exchange) or not self.exchange_stats[exchange]["maker"]:
                continue

            pair = market_info.trading_pair
            # Handle both sides in a single loop
            for side in (TradeType.BUY, TradeType.SELL):
                key = "buy_orders" if side == TradeType.BUY else "sell_orders"

                for proposal in self.optimal_quotes[exchange][key]:
                    self.process_order_proposal(exchange, pair, proposal, side)

    def process_order_proposal(
            self,
            exchange: str,
            pair: str,
            proposal: XEMMArbProposal,
            side: TradeType,
    ) -> None:
        """
        Build, validate, and place a single maker order for a given proposal.
        """
        # Unpack proposal
        if proposal.client_order_id_maker or proposal.market_info_taker is None:
            return

        hedge_ex = proposal.market_info_taker.market.name
        # Skip if hedge exchange is timed out
        if self.time_out_dict.get(hedge_ex):
            return

        # Choose order candidate type
        OrderCls = PerpetualOrderCandidate if self.is_perp[exchange] else OrderCandidate
        order_candidate = OrderCls(
            trading_pair=pair,
            is_maker=True,
            order_type=OrderType.LIMIT,
            order_side=side,
            amount=proposal.amount,
            price=proposal.price_maker,
        )

        # Check minimum size
        if proposal.amount <= 0 or not self.is_above_min_order_size(exchange, order_candidate.amount, order_candidate.price):
            return

        # Place the order
        order_id = (
            self.buy(proposal.market_info_maker, order_candidate.amount, order_candidate.order_type, order_candidate.price)
            if side == TradeType.BUY
            else self.sell(proposal.market_info_maker, order_candidate.amount, order_candidate.order_type, order_candidate.price)
        )

        # Track creation timestamp and link proposal
        if order_id:
            self.order_id_creation_timestamp.append((exchange, order_id, time.perf_counter()))
            proposal.client_order_id_maker = order_id
            self._maker_order_dict.set(order_id, proposal)

    def optimize_order_placement(self, price, is_bid, market_info, pair, optimize_order=True):
        exchange = market_info.market.name

        def amount_sub_my_orders_and_ignore_small_orders(ob_entry):
            amount = ob_entry.amount
            if ob_entry.price in my_limit_orders:
                amount = ob_entry.amount - float(my_limit_orders[ob_entry.price])

            # todo: calculate the max amount to ignore
            return amount if amount > (20 / ob_entry.price) else 0

        my_limit_orders = {}
        min_price_step = self.min_price_step[exchange]
        for (ex, order, order_id) in self.active_limit_orders:
            if ex.display_name == exchange:
                my_limit_orders[float(round(order.price / min_price_step) * min_price_step)] = order.quantity

        # place order just in front of another, don't quote higher than best bid ask
        if is_bid:
            order_book_iterator = market_info.market.get_order_book(pair).bid_entries()
            first_ob_entry = next(order_book_iterator, None)
            if price >= first_ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(first_ob_entry) != 0:
                return market_info.market.quantize_order_price(pair, Decimal(first_ob_entry.price))

            if optimize_order:

                for ob_entry in order_book_iterator:
                    if price == ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return market_info.market.quantize_order_price(pair, Decimal(ob_entry.price))
                    if price > ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return market_info.market.quantize_order_price(pair, Decimal(ob_entry.price) + min_price_step)

        else:
            order_book_iterator = market_info.market.get_order_book(pair).ask_entries()
            first_ob_entry = next(order_book_iterator, None)
            if price <= first_ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(first_ob_entry) != 0:
                return market_info.market.quantize_order_price(pair, Decimal(first_ob_entry.price))

            if optimize_order:
                for ob_entry in order_book_iterator:
                    if price == ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return market_info.market.quantize_order_price(pair, Decimal(ob_entry.price))
                    if price < ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return market_info.market.quantize_order_price(pair, Decimal(ob_entry.price) - min_price_step)

        return market_info.market.quantize_order_price(pair, price)

    def is_perpetual_exchange(self, exchange):
        return True if exchange.endswith("perpetual_testnet") or exchange.endswith("perpetual") else False

    def get_optimal_orders_lists_profit(self, market_info, base_amount, quote_amount, mid):

        # check best hedge price
        exchange = market_info.market.name
        pair = market_info.trading_pair
        buy_orders = []
        sell_orders = []
        possible_prices_list_bid = []
        possible_prices_list_ask = []

        vol = self.volatility_indicator[exchange][pair].current_value_fraction  # vol in decimal
        adjusted_vol = self.adjusted_vol(vol)
        min_profit = self.min_profit_dict[exchange]
        maker_fee = self.exchange_stats[exchange]["maker_fee"]
        optimize_order = self.profit_settings[exchange]["optimize_order"]

        for market_info_hedge in self.all_trading_pair_tuples:
            hedge_exchange_name = market_info_hedge.market.name
            if market_info_hedge.market.name != exchange and self.exchange_stats[hedge_exchange_name]["taker"]:

                pair_ex = market_info_hedge.trading_pair

                available_quote = self.exchange_info[hedge_exchange_name]["quote"]
                available_base = self.exchange_info[hedge_exchange_name]["base"]

                # this is the price for a sell order
                possible_price_bid = market_info_hedge.market.get_price_for_volume(pair_ex, False, min(available_base, (quote_amount / mid))).result_price

                # this is the price for a buy order
                possible_price_ask = market_info_hedge.market.get_price_for_volume(pair_ex, True, min((available_quote / mid), base_amount)).result_price

                if not possible_price_ask.is_nan():  # todo: use some sort of cache
                    possible_prices_list_ask.append((market_info_hedge, possible_price_ask))
                if not possible_price_bid.is_nan():
                    possible_prices_list_bid.append((market_info_hedge, possible_price_bid))

        possible_prices_list_bid = sorted(possible_prices_list_bid, key=lambda item: item[1], reverse=True)
        possible_prices_list_ask = sorted(possible_prices_list_ask, key=lambda item: item[1], reverse=False)

        # check if there is enough capital to hedge on the exchange
        for i in range(len(possible_prices_list_ask)):
            market_info_hedge_ask = possible_prices_list_ask[i][0]
            hedge_price_ask = possible_prices_list_ask[i][1]
            hedge_exchange_ask = market_info_hedge_ask.market.name
            taker_fee = self.exchange_stats[hedge_exchange_ask]["taker_fee"]
            available_quote = self.exchange_info[hedge_exchange_ask]["quote"]
            maker_ask = hedge_price_ask * (Decimal(1) + adjusted_vol + maker_fee + taker_fee + min_profit)

            if base_amount <= available_quote / hedge_price_ask:
                if self.is_above_min_order_size(exchange, base_amount, maker_ask) and self.is_above_min_order_size(hedge_exchange_ask, base_amount, hedge_price_ask):
                    maker_ask = self.optimize_order_placement(maker_ask, False, market_info, pair, optimize_order)
                    quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(base_amount))
                    sell_orders.append(XEMMArbProposal(
                        market_info_maker=market_info,
                        market_info_taker=market_info_hedge_ask,
                        is_buy_maker=False,
                        is_buy_taker=True,
                        price_maker=maker_ask,
                        price_taker=hedge_price_ask,
                        amount=quantized_amount
                    ))
                    break
            else:
                if self.is_above_min_order_size(exchange, available_quote / mid, maker_ask) and self.is_above_min_order_size(hedge_exchange_ask, base_amount, hedge_price_ask):
                    maker_ask = self.optimize_order_placement(maker_ask, False, market_info, pair, optimize_order)
                    quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(available_quote / hedge_price_ask))
                    sell_orders.append(XEMMArbProposal(
                        market_info_maker=market_info,
                        market_info_taker=market_info_hedge_ask,
                        is_buy_maker=False,
                        is_buy_taker=True,
                        price_maker=maker_ask,
                        price_taker=hedge_price_ask,
                        amount=quantized_amount
                    ))
                    base_amount -= available_quote / hedge_price_ask

        for i in range(len(possible_prices_list_bid)):
            market_info_hedge_bid = possible_prices_list_bid[i][0]
            hedge_price_bid = possible_prices_list_bid[i][1]
            hedge_exchange_bid = market_info_hedge_bid.market.name
            taker_fee = self.exchange_stats[hedge_exchange_bid]["taker_fee"]
            available_base = self.exchange_info[hedge_exchange_bid]["base"]
            maker_bid = hedge_price_bid * (Decimal(1) - adjusted_vol - maker_fee - taker_fee - min_profit)

            if quote_amount / mid <= available_base:
                if self.is_above_min_order_size(exchange, quote_amount / mid, maker_bid) and self.is_above_min_order_size(hedge_exchange_bid, quote_amount / mid, maker_bid):
                    maker_bid = self.optimize_order_placement(maker_bid, True, market_info, pair, optimize_order)
                    quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(quote_amount / mid))
                    buy_orders.append(XEMMArbProposal(
                        market_info_maker=market_info,
                        market_info_taker=market_info_hedge_bid,
                        is_buy_maker=True,
                        is_buy_taker=False,
                        price_maker=maker_bid,
                        price_taker=hedge_price_bid,
                        amount=quantized_amount
                    ))
                    break
            else:
                if self.is_above_min_order_size(exchange, available_base, maker_bid) and self.is_above_min_order_size(hedge_exchange_bid, quote_amount / mid, maker_bid):
                    maker_bid = self.optimize_order_placement(maker_bid, True, market_info, pair, optimize_order)
                    quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(available_base))
                    buy_orders.append(XEMMArbProposal(
                        market_info_maker=market_info,
                        market_info_taker=market_info_hedge_bid,
                        is_buy_maker=True,
                        is_buy_taker=False,
                        price_maker=maker_bid,
                        price_taker=hedge_price_bid,
                        amount=quantized_amount
                    ))
                    quote_amount -= available_base * mid

        return buy_orders, sell_orders

    def get_optimal_orders_lists_market_making(self, market_info, base_amount, quote_amount, mid):

        exchange = market_info.market.name
        pair = market_info.trading_pair
        buy_orders = []
        sell_orders = []

        possible_prices_list_bid_dict = {}
        possible_prices_list_ask_dict = {}
        for i in range(self.market_making_settings["number_of_orders"]):
            possible_prices_list_ask_dict[i] = []
            possible_prices_list_bid_dict[i] = []

        vol = self.volatility_indicator[exchange][pair].current_value_fraction  # vol in decimal
        adjusted_vol = self.adjusted_vol(vol)
        maker_fee = self.exchange_stats[exchange]["maker_fee"]

        #  create possible_prices_list
        # todo: this can be optimized, as not all sell orders are placed on the same exchange
        for market_info_hedge in self._all_trading_pair_tuples_list:
            ex = market_info_hedge.market.name
            pair_ex = market_info_hedge.trading_pair

            if ex != exchange and self.exchange_stats[ex]["taker"]:
                additional_amount_base = s_decimal_zero
                for i in range(self.market_making_settings["number_of_orders"]):
                    order_amount_base = self.market_making_settings["order_amounts_quote"][i] / mid
                    order_amount_quote = self.market_making_settings["order_amounts_quote"][i]

                    additional_amount_quote = additional_amount_base * mid
                    top_depth_tolerance_taker_factor = Decimal("1") + (self.market_making_settings["top_depth_tolerance_taker_pct"][i] / Decimal("100"))

                    quote_amount_buy_order = (min((self.exchange_info[ex]["quote"] / mid), order_amount_base) + additional_amount_quote) * top_depth_tolerance_taker_factor
                    base_amount_sell_order = (min(self.exchange_info[ex]["base"], (order_amount_quote / mid)) + additional_amount_base) * top_depth_tolerance_taker_factor

                    # this is the price for a sell order
                    possible_price_bid = market_info_hedge.market.get_price_for_volume(pair_ex, False, base_amount_sell_order).result_price

                    # this is the price for a buy order
                    possible_price_ask = market_info_hedge.market.get_price_for_volume(pair_ex, True, quote_amount_buy_order).result_price

                    if not possible_price_ask.is_nan():
                        possible_prices_list_ask_dict[i].append((market_info_hedge, possible_price_ask))
                    if not possible_price_bid.is_nan():
                        possible_prices_list_bid_dict[i].append((market_info_hedge, possible_price_bid))

                    additional_amount_base += order_amount_base

        # sort
        for i in range(self.market_making_settings["number_of_orders"]):
            possible_prices_list_bid_dict[i] = sorted(possible_prices_list_bid_dict[i], key=lambda item: item[1], reverse=True)
            possible_prices_list_ask_dict[i] = sorted(possible_prices_list_ask_dict[i], key=lambda item: item[1], reverse=False)

        #  create orders
        exchange_info = copy.deepcopy(self.exchange_info)  # need to deepcopy in order to not change the original dict
        for i in range(self.market_making_settings["number_of_orders"]):
            min_profit = Decimal(self.market_making_settings["min_profitability"][i])
            optimize_order = self.market_making_settings["optimize_order"][i]

            # sell orders
            for j in range(len(possible_prices_list_ask_dict[i])):
                market_info_hedge_ask = possible_prices_list_ask_dict[i][j][0]
                hedge_exchange_ask = market_info_hedge_ask.market.name
                hedge_price_ask = possible_prices_list_ask_dict[i][j][1]
                taker_fee = self.exchange_stats[hedge_exchange_ask]["taker_fee"]
                available_quote = exchange_info[hedge_exchange_ask]["quote"]
                maker_ask = hedge_price_ask * (Decimal(1) + adjusted_vol + maker_fee + taker_fee + min_profit)

                order_amount_base = min(self.market_making_settings["order_amounts_quote"][i] / mid, base_amount)

                if order_amount_base <= available_quote / hedge_price_ask:
                    if self.is_above_min_order_size(exchange, order_amount_base, maker_ask) and self.is_above_min_order_size(hedge_exchange_ask, order_amount_base, hedge_price_ask):
                        maker_ask = self.optimize_order_placement(maker_ask, False, market_info, pair, optimize_order)
                        quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(order_amount_base))
                        sell_orders.append(XEMMArbProposal(
                            market_info_maker=market_info,
                            market_info_taker=market_info_hedge_ask,
                            is_buy_maker=False,
                            is_buy_taker=True,
                            price_maker=maker_ask,
                            price_taker=hedge_price_ask,
                            amount=quantized_amount
                        ))
                        base_amount -= order_amount_base
                        exchange_info[hedge_exchange_ask]["quote"] -= order_amount_base * hedge_price_ask
                        break
                elif j == len(possible_prices_list_ask_dict[i]) - 1:  # deploy what ever is left to the best price exchange
                    market_info_hedge_ask = possible_prices_list_ask_dict[i][0][0]
                    hedge_exchange_ask = market_info_hedge_ask.market.name
                    hedge_price_ask = possible_prices_list_ask_dict[i][0][1]
                    available_quote = exchange_info[hedge_exchange_ask]["quote"]
                    maker_ask = hedge_price_ask * (Decimal(1) + adjusted_vol + maker_fee + taker_fee + min_profit)
                    order_amount_base = available_quote / hedge_price_ask
                    if self.is_above_min_order_size(exchange, order_amount_base, maker_ask) and self.is_above_min_order_size(hedge_exchange_ask, order_amount_base, hedge_price_ask):
                        maker_ask = self.optimize_order_placement(maker_ask, False, market_info, pair, optimize_order)
                        quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(order_amount_base))
                        sell_orders.append(XEMMArbProposal(
                            market_info_maker=market_info,
                            market_info_taker=market_info_hedge_ask,
                            is_buy_maker=False,
                            is_buy_taker=True,
                            price_maker=maker_ask,
                            price_taker=hedge_price_ask,
                            amount=quantized_amount
                        ))
                        base_amount -= order_amount_base
                        exchange_info[hedge_exchange_ask]["quote"] -= order_amount_base * hedge_price_ask
                        break

            # buy orders
            for j in range(len(possible_prices_list_bid_dict[i])):
                market_info_hedge_bid = possible_prices_list_bid_dict[i][j][0]
                hedge_exchange_bid = market_info_hedge_bid.market.name
                hedge_price_bid = possible_prices_list_bid_dict[i][j][1]
                taker_fee = self.exchange_stats[hedge_exchange_bid]["taker_fee"]
                available_base = exchange_info[hedge_exchange_bid]["base"]
                maker_bid = hedge_price_bid * (Decimal(1) - adjusted_vol - maker_fee - taker_fee - min_profit)

                order_amount_base = min(self.market_making_settings["order_amounts_quote"][i] / mid, quote_amount / mid)

                if order_amount_base <= available_base:
                    if self.is_above_min_order_size(exchange, order_amount_base, maker_bid) and self.is_above_min_order_size(hedge_exchange_bid, order_amount_base, hedge_price_bid):
                        maker_bid = self.optimize_order_placement(maker_bid, True, market_info, pair, optimize_order)
                        quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(order_amount_base))
                        buy_orders.append(XEMMArbProposal(
                            market_info_maker=market_info,
                            market_info_taker=market_info_hedge_bid,
                            is_buy_maker=True,
                            is_buy_taker=False,
                            price_maker=maker_bid,
                            price_taker=hedge_price_bid,
                            amount=quantized_amount
                        ))
                        quote_amount -= order_amount_base * mid
                        exchange_info[hedge_exchange_bid]["base"] -= order_amount_base
                        break
                elif j == len(possible_prices_list_bid_dict[i]) - 1:  # deploy what ever is left to the best price exchange
                    market_info_hedge_bid = possible_prices_list_bid_dict[i][0][0]
                    hedge_exchange_bid = market_info_hedge_bid.market.name
                    hedge_price_bid = possible_prices_list_bid_dict[i][0][1]
                    available_base = exchange_info[hedge_exchange_bid]["base"]
                    maker_bid = hedge_price_bid * (Decimal(1) - adjusted_vol - maker_fee - taker_fee - min_profit)
                    order_amount_base = available_base
                    if self.is_above_min_order_size(exchange, order_amount_base, maker_bid) and self.is_above_min_order_size(hedge_exchange_bid, order_amount_base, hedge_price_bid):
                        maker_bid = self.optimize_order_placement(maker_bid, True, market_info, pair, optimize_order)
                        quantized_amount = market_info.market.quantize_order_amount(market_info.trading_pair, Decimal(order_amount_base))
                        buy_orders.append(XEMMArbProposal(
                            market_info_maker=market_info,
                            market_info_taker=market_info_hedge_bid,
                            is_buy_maker=True,
                            is_buy_taker=False,
                            price_maker=maker_bid,
                            price_taker=hedge_price_bid,
                            amount=quantized_amount
                        ))
                        quote_amount -= order_amount_base * mid
                        exchange_info[hedge_exchange_bid]["base"] -= order_amount_base
                        break

        # format buy_orders and sell_orders
        if len(buy_orders) < self.market_making_settings["number_of_orders"]:
            for i in range(len(buy_orders), self.market_making_settings["number_of_orders"]):
                # buy_orders.append({"amount": s_decimal_zero,
                #                    "price": s_decimal_zero,
                #                    "hedge_exchange": None,
                #                    "hedge_price": None,
                #                    "order_id": None})
                buy_orders.append(XEMMArbProposal(
                    market_info_maker=None,
                    market_info_taker=None,
                    is_buy_maker=True,
                    is_buy_taker=False,
                    price_maker=s_decimal_zero,
                    price_taker=s_decimal_zero,
                    amount=s_decimal_zero
                ))
        if len(sell_orders) < self.market_making_settings["number_of_orders"]:
            for i in range(len(sell_orders), self.market_making_settings["number_of_orders"]):
                sell_orders.append(XEMMArbProposal(
                    market_info_maker=None,
                    market_info_taker=None,
                    is_buy_maker=False,
                    is_buy_taker=True,
                    price_maker=s_decimal_zero,
                    price_taker=s_decimal_zero,
                    amount=s_decimal_zero
                ))
        return buy_orders, sell_orders

    def get_optimal_orders_lists(self, market_info, base_amount, quote_amount, mid):
        if self.mode == "profit":
            return self.get_optimal_orders_lists_profit(market_info, base_amount, quote_amount, mid)
        elif self.mode == "market_making":
            return self.get_optimal_orders_lists_market_making(market_info, base_amount, quote_amount, mid)
        else:
            raise Exception("mode not supported")

    def calculate_optimal_quotes(self):
        """calculate optimal quotes, based on the available hedge options. the output will be stored in a dict called optimal_quotes, with the format:
        optimal_quotes = {
        "Exchange": {
            "buy_orders": [XEMMArbProposal],
            "sell_orders": [XEMMArbProposal]
        }"""

        new_optimal_quotes = {}

        for market_info in self.all_trading_pair_tuples:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            if not self.exchange_stats[exchange]["maker"]:
                continue

            base_amount = self.exchange_info[exchange]["base"]  # AVAX, BTC. ETH,....
            quote_amount = self.exchange_info[exchange]["quote"]  # USDT, USD,.....
            # quote_amount += self.exchange_info[exchange]["balance_quote_from_positions"]
            # base_amount += self.exchange_info[exchange]["balance_base_from_positions"]
            mid_price = market_info.market.get_mid_price(pair)

            # apply max size restriction
            if quote_amount >= self.max_order_size_quote:
                quote_amount = self.max_order_size_quote
            if base_amount >= self.max_order_size_quote / mid_price:
                base_amount = self.max_order_size_quote / mid_price

            if self.is_perp[exchange]:
                base_amount_capital_in_quote = s_decimal_0  # self.exchange_info[exchange]["balance_base_from_positions"]
                quote_amount_capital_in_base = s_decimal_0  # self.exchange_info[exchange]["balance_quote_from_positions"]

                buy_orders_capital_in_quote, sell_orders_capital_in_quote = self.get_optimal_orders_lists(market_info,
                                                                                                          base_amount_capital_in_quote,
                                                                                                          quote_amount,
                                                                                                          mid_price)
                buy_orders_capital_in_base, sell_orders_capital_in_base = self.get_optimal_orders_lists(market_info,
                                                                                                        base_amount,
                                                                                                        quote_amount_capital_in_base,
                                                                                                        mid_price)

                # we use the orders wherever the spread is smallest
                buy_xemm_proposals, sell_xemm_proposals = self.get_best_buy_sell_orders_for_perps(market_info,
                                                                                                  mid_price,
                                                                                                  buy_orders_capital_in_quote,
                                                                                                  sell_orders_capital_in_quote,
                                                                                                  buy_orders_capital_in_base,
                                                                                                  sell_orders_capital_in_base)
            else:
                buy_xemm_proposals, sell_xemm_proposals = self.get_optimal_orders_lists(market_info, base_amount, quote_amount, mid_price)

            new_optimal_quotes[exchange] = {
                "buy_orders": buy_xemm_proposals,
                "sell_orders": sell_xemm_proposals
            }

        # compare old optimal quotes with new

        if self.optimal_quotes:
            if self.mode == "profit":
                self.compare_old_with_new_optimal_quotes_profit(new_optimal_quotes)
            elif self.mode == "market_making":
                self.compare_old_with_new_optimal_quotes_market_making(new_optimal_quotes)
        else:
            self.optimal_quotes = new_optimal_quotes

    def compare_old_with_new_optimal_quotes_market_making(self, new_optimal_quotes):
        place_cancel_dict = {}
        order_sides = ["buy_orders", "sell_orders"]
        tolerance = Decimal(self.market_making_settings["Cancel_order_tolerance"])
        cancel_order_threshold = Decimal(self.market_making_settings["Cancel_order_threshold"])

        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            if not self.exchange_stats[exchange]["maker"]:
                continue

            for order_side in order_sides:
                for index, (old_order_entry, new_order_entry) in enumerate(zip(self.optimal_quotes[exchange][order_side], new_optimal_quotes[exchange][order_side])):

                    if old_order_entry.client_order_id_maker:
                        order_id = old_order_entry.client_order_id_maker
                        old_order = self.get_active_limit_order_by_id(order_id)

                        # is_buy = old_order.is_buy
                        price = old_order.price
                        creation_timestamp = old_order.creation_timestamp / 1e6  # in flight orders have a resolution of 1S, Limit orders of 0.000001S
                        #hedge_exchange, hedge_price = self.maker_order_id_to_hedge_exchange[order_id]
                        xemm_arb_proposal = self._maker_order_dict.get(order_id)
                        hedge_exchange = xemm_arb_proposal.market_info_taker.market.name
                        hedge_price = xemm_arb_proposal.price_taker

                        order_to_old = True if creation_timestamp + self.max_order_age_seconds < self.current_timestamp else False

                        # check if to old
                        if order_to_old:
                            place_cancel_dict[order_id] = xemm_arb_proposal.market_info_maker
                            continue

                        if order_side == "buy_orders":
                            threshold_max = new_order_entry.price_maker * (Decimal(1) - cancel_order_threshold)
                        else:
                            threshold_max = new_order_entry.price_maker * (Decimal(1) + cancel_order_threshold)

                        keep_order = False
                        if hedge_exchange == new_order_entry.market_info_taker.market.name  and (
                                (order_side == "buy_orders" and new_order_entry.price_maker + tolerance >= price > threshold_max) or (
                                order_side == "sell_orders" and new_order_entry.price_maker - tolerance <= price < threshold_max)):
                            # keep order
                            new_optimal_quotes[exchange][order_side][index] = xemm_arb_proposal
                            keep_order = True

                        # cancel order if no equivalent is found
                        if not keep_order:
                            place_cancel_dict[order_id] = xemm_arb_proposal.market_info_maker

        # cancel orders below min profit or after max order age
        for o_id, market_info in place_cancel_dict.items():
            # todo: KeyError,  if order got manually stop_tracking_order
            ex = market_info.market.name
            current_state = market_info.market.in_flight_orders[o_id].current_state
            if current_state != OrderState.PENDING_CANCEL:
                self.logger().info(f"calculate_optimal_quotes: cancel order {ex}: {o_id}, current_state: {current_state}")
                self.ids_to_cancel.add(o_id)
                safe_ensure_future(self.async_cancel(market_info, o_id))

        # re assign optimal quotes
        self.optimal_quotes = new_optimal_quotes

    def compare_old_with_new_optimal_quotes_profit(self, new_optimal_quotes):
        place_cancel_dict = {}
        for market, _, order_id in self.active_limit_orders:
            exchange = market.name
            if order_id not in self.ids_to_cancel:
                old_order = self.get_active_limit_order_by_id(order_id)
                is_buy = old_order.is_buy
                price = old_order.price
                creation_timestamp = old_order.creation_timestamp / 1e6  # in flight orders have a resolution of 1S, Limit orders of 0.000001S
                xemm_arb_proposal = self._maker_order_dict.get(order_id)
                hedge_exchange = xemm_arb_proposal.market_info_taker.market.name
                hedge_price = xemm_arb_proposal.price_taker
                order_to_old = True if creation_timestamp + self.max_order_age_seconds < self.current_timestamp else False

                # check if to old
                if order_to_old:
                    place_cancel_dict[order_id] = xemm_arb_proposal.market_info_maker
                    break

                # check if it exists in the new quotes
                order_side = "buy_orders" if is_buy else "sell_orders"
                keep_order = False

                for index, xemm_arb_proposal_new in enumerate(new_optimal_quotes[exchange][order_side]):
                    if order_side == "buy_orders":
                        threshold_max = xemm_arb_proposal_new.price_maker * (Decimal(1) - Decimal(0.0008))
                    else:
                        threshold_max = xemm_arb_proposal_new.price_maker * (Decimal(1) + Decimal(0.0008))

                    # check if this order matches +-

                    if hedge_exchange == xemm_arb_proposal_new.market_info_taker.market.name and (
                            (order_side == "buy_orders" and xemm_arb_proposal_new.price_maker >= price > threshold_max) or (
                            order_side == "sell_orders" and xemm_arb_proposal_new.price_maker <= price < threshold_max)):
                        # keep order
                        new_optimal_quotes[exchange][order_side][index] = xemm_arb_proposal
                        keep_order = True
                        break

                # cancel order if no equivalent is found
                if not keep_order:
                    place_cancel_dict[order_id] = xemm_arb_proposal.market_info_maker

        # cancel orders below min profit or after max order age
        for o_id, market_info in place_cancel_dict.items():
            # todo: KeyError,  if order got manually stop_tracking_order
            ex = market_info.market.name
            current_state = market_info.market.in_flight_orders[o_id].current_state
            if current_state != OrderState.PENDING_CANCEL:
                self.logger().info(f"calculate_optimal_quotes: cancel order {ex}: {o_id}, current_state: {current_state}")
                self.ids_to_cancel.add(o_id)
                safe_ensure_future(self.async_cancel(market_info, o_id))

        # re assign optimal quotes
        self.optimal_quotes = new_optimal_quotes

    def get_best_buy_sell_orders_for_perps(self, market_info, mid_price, buy_orders_capital_in_quote, sell_orders_capital_in_quote, buy_orders_capital_in_base, sell_orders_capital_in_base):
        exchange = market_info.market.name
        # we all ready calculated the prices
        # merge orders under capital constraints and compare the spreads
        # buy_orders_capital_in_quote and sell_orders_capital_in_base essentially compete for the same capital
        total_amount_available_base = self.exchange_info[exchange]["base"] + self.exchange_info[exchange]["balance_base_from_positions"]
        total_amount_available_quote = self.exchange_info[exchange]["quote"] + self.exchange_info[exchange]["balance_quote_from_positions"]
        buy_orders, sell_orders = [], []
        index_sell, index_buy = 0, 0

        while index_buy < len(buy_orders_capital_in_quote) or index_sell < len(sell_orders_capital_in_base):

            spread_buy = abs(buy_orders_capital_in_quote[index_buy].price_maker - mid_price) if index_buy < len(buy_orders_capital_in_quote) else Decimal("inf")
            spread_sell = abs(sell_orders_capital_in_base[index_sell].price_maker - mid_price) if index_sell < len(sell_orders_capital_in_base) else Decimal("inf")

            if index_buy < len(buy_orders_capital_in_quote):
                buy_orders_capital_in_quote[index_buy].amount = min(buy_orders_capital_in_quote[index_buy].amount, total_amount_available_quote / mid_price)
            if index_sell < len(sell_orders_capital_in_base):
                sell_orders_capital_in_base[index_sell].amount = min(sell_orders_capital_in_base[index_sell].amount, total_amount_available_base) if index_sell < len(
                    sell_orders_capital_in_base) else s_decimal_zero

            # check if buy
            if spread_buy <= spread_sell and total_amount_available_quote >= buy_orders_capital_in_quote[index_buy].amount * mid_price:
                buy_orders.append(buy_orders_capital_in_quote[index_buy])
                total_amount_available_base -= buy_orders_capital_in_quote[index_buy].amount
                total_amount_available_quote -= buy_orders_capital_in_quote[index_buy].amount * buy_orders_capital_in_quote[index_buy].price_maker
                index_buy += 1

            # check if sell
            elif spread_sell < spread_buy and total_amount_available_base >= sell_orders_capital_in_base[index_sell].amount:
                sell_orders.append(sell_orders_capital_in_base[index_sell])
                total_amount_available_base -= sell_orders_capital_in_base[index_sell].amount
                total_amount_available_quote -= sell_orders_capital_in_base[index_sell].amount * sell_orders_capital_in_base[index_sell].price_maker
                index_sell += 1

            else:
                break

        return buy_orders, sell_orders

    def adjusted_vol(self, vol):
        """vol needs to be in decimal. not percent"""
        if vol <= Decimal(0.0005):
            return Decimal(0)
        else:
            adjusted_vol = (vol - Decimal(0.0005))
            return adjusted_vol * self.volatility_to_spread_multiplier

    def is_above_min_order_size(self, exchange, amount, price):
        return True if self.min_notional_size_dict[exchange] * Decimal(1.1) < (amount * price) and self.min_os_size_dict[exchange] < amount else False

    def check_balances_and_quotes_perp(self, market_info):
        exchange = market_info.market.name
        pair = market_info.trading_pair
        base, quote = pair.split("-")
        mid_price = market_info.market.get_mid_price(pair)
        balance_base_from_positions = Decimal(0)
        balance_quote_from_positions = Decimal(0)
        balance_quote = market_info.market.get_available_balance(quote)
        balance_base = balance_quote / mid_price

        # check open orders
        for order_ in market_info.market.in_flight_orders.values():
            amount = order_.amount
            executed_amount = order_.executed_amount_base
            trade_type = order_.trade_type
            is_buy = True if trade_type == TradeType.BUY else False
            price = order_.price
            current_state = order_.current_state

            # skip if order is pending create
            if current_state == OrderState.PENDING_CREATE and not exchange == "gate_io_perpetual":
                continue

            if not executed_amount.is_nan():
                open_amount = amount - executed_amount
            else:
                open_amount = amount

            if is_buy:
                balance_base += open_amount
                balance_quote += (open_amount * price)

            elif not is_buy and not exchange == "gate_io_perpetual":
                balance_base += open_amount
                balance_quote += (open_amount * price)

        # check open positions in futures exchanges
        long_pos = s_decimal_0  # in base
        short_pos = s_decimal_0  # in base
        if pair in market_info.market.account_positions.keys():
            position = market_info.market.account_positions[pair]
            if position.position_side == PositionSide.LONG:
                balance_base_from_positions += position.amount
                balance_base += position.amount
                long_pos += position.amount
            else:
                balance_quote_from_positions += abs(position.amount * mid_price)
                balance_quote += abs(position.amount * mid_price)
                short_pos += abs(position.amount)

        # check open hedge amount. this is not exchange specific
        balance_quote -= max(self._global_unhedged_position, s_decimal_zero) * mid_price
        balance_base += min(self._global_unhedged_position, s_decimal_zero)

        # for hedge_id, info in self.hedge_trades.items():
        #     if info["status"] in ["failed", "in_process", "processed"]:
        #         hedge_is_buy = info["is_buy"]
        #         hedge_amount = info["amount"]
        #         if hedge_is_buy:
        #             balance_quote -= (hedge_amount * mid_price)
        #         else:
        #             balance_base -= hedge_amount

        # add max position restriction

        max_pos = self.exchange_stats[exchange]["max_position_in_quote"]
        balance_quote = min(balance_quote, (max_pos - (long_pos * mid_price)))
        balance_base = min(balance_base, ((max_pos / mid_price) - short_pos))

        self.exchange_info[exchange] = {"base": balance_base,
                                        "quote": balance_quote,
                                        "balance_base_from_positions": balance_base_from_positions,
                                        "balance_quote_from_positions": balance_quote_from_positions}

    def check_balances_and_quotes(self):
        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            base, quote = pair.split("-")
            mid_price = market_info.market.get_mid_price(pair)
            balance_base_from_positions = Decimal(0)
            balance_quote_from_positions = Decimal(0)
            is_perp = self.is_perp[exchange]
            if is_perp:
                self.check_balances_and_quotes_perp(market_info)
                continue

            # check free balances
            balance_quote = market_info.market.get_available_balance(quote)
            balance_base = market_info.market.get_available_balance(base)
            # check open orders balances
            for order_ in market_info.market.in_flight_orders.values():
                amount = order_.amount
                executed_amount = order_.executed_amount_base
                trade_type = order_.trade_type
                is_buy = True if trade_type == TradeType.BUY else False
                price = order_.price
                current_state = order_.current_state

                # skip if order is pending create
                if current_state == OrderState.PENDING_CREATE:
                    continue

                if not executed_amount.is_nan():
                    open_amount = amount - executed_amount
                else:
                    open_amount = amount

                if is_buy:
                    # add to balance to quote
                    balance_quote += (open_amount * price)
                elif not is_buy:
                    # add to balance to base
                    balance_base += open_amount

            # check open hedge amount. this is not exchange specific
            for hedge_id, info in self.hedge_trades.items():
                if info["status"] in ["failed", "in_process"]:
                    hedge_is_buy = info["is_buy"]
                    hedge_amount = info["amount"]
                    if hedge_is_buy:
                        balance_quote -= (hedge_amount * mid_price)
                    else:
                        balance_base -= hedge_amount

            # apply idle amounts and slippage buffer for Limit hedge orders
            # todo: this is not perfect, as it does not take into account the actual amount that is used for the hedge, but uses the total amount
            taker_order_type = self.taker_order_type if self.taker_order_type in market_info.market.supported_order_types() else OrderType.LIMIT
            base_amount_for_slippage_tolerance = (balance_base * self.hedge_order_slippage_tolerance) if taker_order_type == OrderType.LIMIT else Decimal(0)
            quote_amount_for_slippage_tolerance = (balance_quote * self.hedge_order_slippage_tolerance) if taker_order_type == OrderType.LIMIT else Decimal(0)

            if balance_base <= (self.idle_amount_in_quote / mid_price) + base_amount_for_slippage_tolerance:
                balance_base = Decimal(0)
            else:
                balance_base -= (self.idle_amount_in_quote / mid_price) + base_amount_for_slippage_tolerance

            if balance_quote <= self.idle_amount_in_quote + quote_amount_for_slippage_tolerance:
                balance_quote = Decimal(0)
            else:
                balance_quote -= self.idle_amount_in_quote + quote_amount_for_slippage_tolerance

            self.exchange_info[exchange] = {"base": balance_base,
                                            "quote": balance_quote,
                                            "balance_base_from_positions": balance_base_from_positions,
                                            "balance_quote_from_positions": balance_quote_from_positions}

    def get_exchange_from_order_id(self, order_id):
        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            for inflight_order in market_info.market.in_flight_orders.values():
                if order_id == inflight_order.client_order_id:
                    return exchange
        return "exchange not found"

    async def async_cancel(self, market_info, order_id, cancel_try_count=0, await_creation=False, await_cancellation=False):
        exchange = market_info.market.name
        trading_pair = market_info.trading_pair
        try:
            order = market_info.market.in_flight_orders[order_id]
            current_state = order.current_state
            exchange_order_id = order.exchange_order_id
        except KeyError:
            self.logger().info(f"async_cancel: Abort cancellation since order could not be found: {order_id}:")
            self.ids_to_cancel.discard(order_id)
            return

        if cancel_try_count > 5:
            self.logger().info(f"async_cancel: cancel trys reached limit for {order_id}:")
            self.ids_to_cancel.discard(order_id)
            return

        if current_state == OrderState.PENDING_CANCEL or current_state == OrderState.FILLED:
            self.logger().info(f"async_cancel: Abort cancellation since current_state is {current_state} for {order_id}:")
            return

        # handle exchanges that don't handle cancel pending create:
        special_treatment_exchanges = ["ascend_ex", "mexc"]
        if await_creation or (current_state == OrderState.PENDING_CREATE and (exchange in special_treatment_exchanges or exchange_order_id is None)):

            self.logger().info(f"async_cancel: Waiting for order creation event for {order_id}:")

            if order_id not in self.order_creation_events:
                self.order_creation_events[order_id] = asyncio.Event()
            await self.order_creation_events[order_id].wait()

            self.order_creation_events.pop(order_id, None)

            self.logger().info(f"async_cancel: Order created, proceeding to cancel {order_id}")

        if await_cancellation:
            self.order_cancel_events[order_id] = asyncio.Event()

        def check_exchange_id(market_info, order_id):
            """
                Parameters:
                - exchange (str): The name of the exchange to check the order in.
                - id (str): The ID of the order to check.

                Returns:
                - int: Returns 1 if the order is found and has no valid exchange order ID, hence stopped tracking.
                       Returns 0 if the order does not meet the criteria for stopping tracking or is not found.
                """
            if order_id in market_info.market.in_flight_orders.keys():
                order = market_info.market.in_flight_orders[order_id]
                current_state = order.current_state
                exchange_order_id = order.exchange_order_id

                self.logger().info(f"async_cancel: order: {order_id}, exchange_order_id: {exchange_order_id}, current_state: {current_state}")
                if (exchange_order_id is None or exchange_order_id == "None") and current_state == OrderState.OPEN:
                    self.logger().info(f"async_cancel: order: {order_id} has no exchange id: {exchange_order_id} will stop tracking the order!")
                    market_info.market.stop_tracking_order(order_id)

                    # perform clean up
                    self._maker_order_dict.delete(order_id)
                    self.maker_order_id_to_hedge_exchange.pop(exchange, None)
                    if self.is_perp[exchange]:
                        safe_ensure_future(market_info.market._update_balances())
                    self.ids_to_cancel.discard(order_id)
                    return 1
            return 0

        # check exchange order id
        if check_exchange_id(market_info, order_id) == 1:
            return

        try:
            async with timeout(10.0):
                try:
                    result = await safe_gather(market_info.market._execute_cancel(trading_pair, order_id), return_exceptions=True)

                except AttributeError:
                    result = await safe_gather(market_info.market.execute_cancel(trading_pair, order_id), return_exceptions=True)

                if result[0] is None:
                    if check_exchange_id(market_info, order_id) == 0:
                        # cancel again!
                        await asyncio.sleep(0.1)
                        await safe_gather(self.async_cancel(market_info, order_id, cancel_try_count + 1))

        except Exception:
            self.logger().network(
                "async_cancel: Unexpected error canceling orders (update orders).",
                exc_info=True,
                app_warning_msg=f"Failed to cancel order with {exchange} . Check API key and network connection.")

            check_exchange_id(market_info, order_id)
        self.ids_to_cancel.discard(order_id)

        if await_cancellation:
            self.logger().info(f"async_cancel: Await Order cancellation {order_id}")
            await self.order_cancel_events[order_id].wait()

    def cancel_all_maker(self):
        for (order_exchange, order, order_id) in self.active_maker_limit_orders:
            self.cancel(order_exchange.display_name, order.trading_pair, order_id)

    async def execute_hedge(self,
                            xemm_trade: XEMMTrade,
                            amount: Decimal,
                            retry_count: int = 0) -> None:
        """
        Places (and waits for) the taker hedge order, cancelling any
        conflicting maker orders first.
        """
        taker_info = xemm_trade.market_info_taker
        exchange = taker_info.market.name
        pair = taker_info.trading_pair
        is_buy_order = xemm_trade.is_buy_taker
        is_perp = self.is_perp[exchange]
        base, quote = pair.split("-")

        # cancel any conflicting maker orders
        async_cancel_tasks: list[Awaitable[Any]] = []
        for ex, _, order_id in self.active_maker_limit_orders:
            if ex.display_name != exchange:
                continue

            in_flight = taker_info.market.in_flight_orders.get(order_id)
            if in_flight is None:
                self.logger().debug("Maker order %s not found on %s", order_id, exchange)
                continue

            maker_is_buy = in_flight.trade_type == TradeType.BUY
            if is_perp or maker_is_buy == is_buy_order:  # spot → cancel only if same side
                self.logger().info(f"execute_hedge: cancelling maker {order_id} on {exchange}")
                self.time_out_dict[exchange] = True
                self._maker_order_dict.delete(order_id)

                if exchange in self._await_cancelation_exchanges_set:
                    async_cancel_tasks.append(self.async_cancel(taker_info, order_id, await_cancellation=True))
                else:
                    self.cancel(taker_info, order_id)

        if async_cancel_tasks:
            await safe_gather(*async_cancel_tasks)

        # place hedge order
        best_quote = taker_info.market.get_price_for_volume(pair, is_buy_order, amount).result_price
        order_amount = taker_info.market.quantize_order_amount(pair, amount)
        slip_factor = (Decimal("1") + self.hedge_order_slippage_tolerance if is_buy_order else Decimal("1") - self.hedge_order_slippage_tolerance)
        taker_order_type = self.taker_order_type if self.taker_order_type in taker_info.market.supported_order_types() else OrderType.LIMIT
        order_price = best_quote * slip_factor if taker_order_type == OrderType.LIMIT else best_quote
        order_id = (self.buy(taker_info, order_amount, taker_order_type, order_price) if is_buy_order else self.sell(taker_info, order_amount, taker_order_type, order_price))
        self.order_id_creation_timestamp.append((exchange, order_id, time.perf_counter()))

        self.logger().info(
            "execute_hedge: %s %s | amt=%s px=%s (best=%s) "
            "base_bal=%s quote_bal=%s order_id=%s",
            "BUY" if is_buy_order else "SELL", exchange,
            order_amount, order_price, best_quote,
            taker_info.market.get_available_balance(base),
            taker_info.market.get_available_balance(quote),
            order_id,
        )

        # track the order
        xemm_trade.client_order_id_taker = order_id
        self._taker_order_dict.set(order_id, xemm_trade)

        try:
            await xemm_trade.wait_until_done()
        except RuntimeError as e:
            self.logger().info(f"execute_hedge: retry_count: {retry_count} is_failed: {xemm_trade.is_failed} {e}")
            if retry_count < 1 and xemm_trade.is_failed:
                self.handle_failed_hedge(xemm_trade)

    def handle_failed_hedge(self, xemm_trade: XEMMTrade):

        self.logger().info("handle_failed_hedge:")
        self.log_inflight_orders()

        is_buy = xemm_trade.is_buy_taker
        amount = xemm_trade.amount

        # check where to hedge, we include all exchanges, as the prices might have diverged
        best_available_price = float('inf') if is_buy else float('-inf')
        best_available_exchange = None

        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            base, quote = pair.split("-")
            available_base_amount = self.exchange_info[exchange]["base"]
            available_quote_amount = self.exchange_info[exchange]["quote"]
            mid_price = market_info.market.get_mid_price(pair)
            if (available_quote_amount / mid_price >= amount) if is_buy else (available_base_amount >= amount):

                available_price = market_info.market.get_price_for_volume(pair, is_buy, amount).result_price
                if (is_buy and available_price < best_available_price) or (not is_buy and available_price > best_available_price):
                    best_available_price = available_price
                    best_available_exchange = exchange

        if best_available_exchange:
            self.logger().info(f"handle_failed_hedge: re submit hedge: amount {amount}, is_buy: {is_buy}, best_available_exchange: {best_available_exchange} : ")
            loop = asyncio.get_event_loop()
            loop.create_task(self.execute_hedge(xemm_trade, amount, 1))

        else:
            msg = f"handle_failed_hedge: no hedge possible for maker id: {xemm_trade.client_order_id_maker}, amount: {amount}, is_buy: {is_buy}"
            self.logger().info(msg)
            self.notify_hb_app_with_timestamp(msg)

    def apply_hanging_and_global(
            self,
            proposal: XEMMArbProposal,
            amount: Decimal,
    ) -> Decimal:
        """
        Incorporate any hanging and global unhedged amounts into the hedge amount.
        """
        if proposal.hanging_amount > s_decimal_zero:
            self.logger().info(f"place_hedge: adding hanging amount {proposal.hanging_amount}")
            amount += proposal.hanging_amount
            proposal.hanging_amount = s_decimal_zero

        if self._global_unhedged_position != s_decimal_zero:
            pos = self._global_unhedged_position
            self.logger().info(f"place_hedge: applying global unhedged position {pos}")
            if proposal.is_buy_taker:
                new_amt = max(s_decimal_zero, amount - pos)
                self._global_unhedged_position -= (amount - new_amt)
            else:
                new_amt = max(s_decimal_zero, amount + pos)
                self._global_unhedged_position += (amount - new_amt)
            amount = new_amt

        return amount

    def get_xemm_trade(
            self,
            proposal: XEMMArbProposal,
            amount: Decimal,
    ) -> Optional[Tuple[XEMMTrade, Decimal]]:
        """
        Prepare and dispatch a hedge order asynchronously, applying hanging/global adjustments
        and validating against minimum size.
        """
        hedge_ex = proposal.market_info_taker.market.name
        maker_id = proposal.client_order_id_maker

        amount = self.apply_hanging_and_global(proposal, amount)
        if amount == s_decimal_zero:
            return

        mid = proposal.market_info_taker.get_mid_price()
        quant_amt = proposal.market_info_taker.market.quantize_order_amount(proposal.market_info_taker.trading_pair, amount)
        self._global_unhedged_position += (amount - quant_amt) if proposal.is_buy_maker else -(amount - quant_amt)

        if not self.is_above_min_order_size(hedge_ex, quant_amt, mid):
            self.logger().info(f"place_hedge: size {quant_amt} below min for {hedge_ex}")
            proposal.add_hanging_amount(amount)
            return

        self.logger().info(f"place_hedge: placing hedge for {maker_id}, amount {quant_amt} on {hedge_ex}")
        xemm_trade = proposal.get_xemm_trade_for_amount(amount)
        return xemm_trade, amount


    # format status
    def format_status(self) -> str:
        """
        Returns status of the current strategy on user balances and current active orders. This function is called
        when status command is issued. Override this function to create custom status display output.
        """
        if not self._all_markets_ready:
            return "Market connectors are not ready."
        lines = []

        exchange_stats_df = self.get_exchange_stats_df()
        lines.extend(["", "  Exchange stats:"] + ["    " + line for line in exchange_stats_df.to_string(index=False).split("\n")])

        time_diffs = np.diff(self.ob_update_event_timestamps)
        mean_diff = np.mean(time_diffs)
        lines.extend(["", f" on_tick_runtime: {self.on_tick_runtime:.4f} ms  mean_ob_diff: {mean_diff:.1f} ms"])

        balance_df = self.get_balance_df()
        lines.extend(["", "  Balances:"] + ["    " + line for line in balance_df.to_string(index=False).split("\n")])

        try:
            positions_df = self.get_positions_df()
            lines.extend(["", "  Positions:"] + ["    " + line for line in positions_df.to_string(index=False).split("\n")])
        except ValueError:
            pass

        try:
            df = self.get_active_maker_trades_df()
            lines.extend(["", "  Maker Orders:"] + ["    " + line for line in df.to_string(index=False).split("\n")])
        except ValueError:
            lines.extend(["", "  No active maker orders."])

        try:
            df = self.get_hedge_trades_df()
            lines.extend(
                ["", "  hedge_trades_dict:"] + ["    " + line for line in df.to_string(index=False).split("\n")])
        except ValueError:
            lines.extend(["", "  No hedge_trades."])

        return "\n".join(lines)

    def get_balance_df(self) -> pd.DataFrame:
        """
        Returns a data frame for all asset balances for displaying purpose.
        """
        columns: List[str] = ["Exchange", "Asset", "Total Balance", "Available Balance"]
        data: List[Any] = []
        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            base, quote = pair.split("-")
            if self.is_perp[exchange]:
                data.append([exchange,
                             quote,
                             float(market_info.market.get_balance(quote)),
                             float(market_info.market.get_available_balance(quote))])
            else:
                data.append([exchange,
                             base,
                             float(market_info.market.get_balance(base)),
                             float(market_info.market.get_available_balance(base))])
                data.append([exchange,
                             quote,
                             float(market_info.market.get_balance(quote)),
                             float(market_info.market.get_available_balance(quote))])
        df = pd.DataFrame(data=data, columns=columns).replace(np.nan, '', regex=True)
        df.sort_values(by=["Exchange", "Asset"], inplace=True)
        return df

    def get_positions_df(self) -> pd.DataFrame:
        """
        Returns a data frame for all asset balances for displaying purpose.
        """
        columns: List[str] = ["ex", "mrkt", "side", "entry_p", "amt", "u_pnl"]
        data: List[Any] = []
        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            if self.is_perp[exchange]:
                if pair in market_info.market.account_positions.keys():
                    position = market_info.market.account_positions[pair]
                    data.append([exchange,
                                 pair,
                                 "buy" if position.position_side == PositionSide.LONG else "sell",
                                 float(position.entry_price),
                                 float(position.amount),
                                 float(position.unrealized_pnl)])

        if not data:
            raise ValueError("No data available.")

        df = pd.DataFrame(data=data, columns=columns).replace(np.nan, '', regex=True)
        df.sort_values(by=["entry_p"], inplace=True)
        return df

    def get_hedge_trades_df(self) -> pd.DataFrame:
        """
        Return a data frame of all active orders for displaying purpose.
        """
        columns = ["maker_ex_trade_id", "Side", "Amount", "Status"]
        data = []

        for ex_trade_id, trade_info in self.hedge_trades.items():
            hedge_is_buy = trade_info["is_buy"]
            hedge_amount = trade_info["amount"]
            hedge_status = trade_info["status"]

            data.append([
                ex_trade_id,
                "buy" if hedge_is_buy else "sell",
                float(hedge_amount),
                hedge_status
            ])

        if not data:
            raise ValueError("No data available.")

        df = pd.DataFrame(data=data, columns=columns)
        df.sort_values(by=["maker_ex_trade_id", "Side"], inplace=True)
        return df

    def get_exchange_stats_df(self):
        """
        Returns a data frame for all volatility for displaying purpose.
        """
        columns: List[str] = ["ex", "vol", "spread", "mid", "vol_add_spread", "rt_latency"]
        data: List[Any] = []
        for market_info in self._all_trading_pair_tuples_list:
            connector_name = market_info.market.name
            pair = market_info.trading_pair
            vol_decimal = self.volatility_indicator[connector_name][pair].current_value_fraction
            vol_pct = self.volatility_indicator[connector_name][pair].current_value_pct
            vol_adjusted = self.adjusted_vol(vol_decimal)
            vol_additional_spread = vol_adjusted * Decimal(100)
            roundtrip_latency = sum(self.latency_roundtrip[connector_name]) / len(self.latency_roundtrip[connector_name]) if self.latency_roundtrip[connector_name] else 0
            spread = ((market_info.market.get_price(pair, True) - market_info.market.get_price(pair, False)) / market_info.market.get_price(pair, True)) * 100

            mid_price = market_info.market.get_mid_price(pair)
            data.append([connector_name,
                         f"{round(vol_pct, 2)}%",
                         f"{round(spread, 2)}%",
                         f"{mid_price}",
                         f"{round(vol_additional_spread, 2)}%",
                         f"{round(roundtrip_latency, 2)} ms"
                         ])

        df = pd.DataFrame(data=data, columns=columns).replace(np.nan, '', regex=True)
        df.sort_values(by=["mid"], ascending=False, inplace=True)
        return df

    def get_active_maker_trades_df(self) -> pd.DataFrame:
        """
        Return a data frame of all active orders for displaying purpose.
        """
        columns = ["ex", "mrkt", "side", "price", "amt", "age", "hedge_ex", "h_spread"]
        data = []
        for ex, order, order_id in self.active_maker_limit_orders:
            age_txt = "n/a" if order.age() <= 0. else pd.Timestamp(order.age(), unit='s').strftime('%H:%M:%S')
            xemm_arb_proposal = self._maker_order_dict.get(order_id)
            exchange_name = ex.display_name
            mid_price = xemm_arb_proposal.market_info_maker.market.get_mid_price(order.trading_pair)
            spread = round(((abs(mid_price - order.price) / mid_price) * 100), 2)
            data.append([
                exchange_name,
                order.trading_pair,
                "buy" if order.is_buy else "sell",
                float(order.price),
                float(order.quantity),
                age_txt,
                xemm_arb_proposal.market_info_taker.market.name,
                f"{spread}%",
            ])
        if not data:
            raise ValueError
        df = pd.DataFrame(data=data, columns=columns)
        df.sort_values(by=["price"], ascending=False, inplace=True)
        return df

    # event handlers
    def did_create_order(self, event):

        if event.order_id in self.order_creation_events:
            self.order_creation_events[event.order_id].set()

    def did_create_buy_order(self, event: BuyOrderCreatedEvent):
        """
        Method called when the connector notifies an order has been created
        """
        self.did_create_order(event)
        self.calculate_latency(event)
        return

    def did_create_sell_order(self, event: SellOrderCreatedEvent):
        """
        Method called when the connector notifies an order has been created
        """
        self.did_create_order(event)
        self.calculate_latency(event)
        return

    def calculate_latency(self, event):

        for exchange, o_id, timestamp in self.order_id_creation_timestamp:
            if o_id == event.order_id:
                latency = (time.perf_counter() - timestamp) * 1000
                self.latency_roundtrip[exchange].append(latency)
                self.order_latency[event.order_id] = latency
                self.order_id_creation_timestamp.remove((exchange, o_id, timestamp))

    async def cancel_order_by_exchange_order_id(self, market_info: MarketTradingPairTuple, exchange_order_id: str, trading_pair: str):

        self.logger().info(f"cancel_order_by_exchange_order_id: exchange_order_id: {exchange_order_id}, trading_pair: {trading_pair}")
        if not hasattr(market_info.market, "_place_cancel_by_exchange_order_id"):
            self.logger().info(f"cancel_order_by_exchange_order_id: exchange: {market_info.market.name} does not support cancel by exchange order id")
            return

        success = await market_info.market._place_cancel_by_exchange_order_id(exchange_order_id=exchange_order_id, trading_pair=trading_pair)

        if not success:
            self.logger().info(f"cancel_order_by_exchange_order_id: failed to cancel order: {exchange_order_id}")
        else:
            self.logger().info(f"cancel_order_by_exchange_order_id: successfully canceled order: {exchange_order_id}")

    def did_fail_order(self, event: MarketOrderFailureEvent):
        """
        Method called when the connector notifies an order has failed
        """
        self.calculate_latency(event)

        # handle if maker order
        if self._maker_order_dict.has(event.order_id):
            xemm_proposal = self._maker_order_dict.get(event.order_id)
            exchange = xemm_proposal.market_info_maker.market.name
            self._maker_order_dict.delete(event.order_id)
            self.time_out_dict[exchange] = False
            self.ids_to_cancel.discard(event.order_id)
            safe_ensure_future(xemm_proposal.market_info_maker.market._update_balances())
            base, quote = xemm_proposal.market_info_maker.base_asset, xemm_proposal.market_info_maker.quote_asset
            self.logger().info(
                f"did_fail_order: {exchange}, quote: {xemm_proposal.market_info_maker.market.get_available_balance(quote)}, base: {xemm_proposal.market_info_maker.market.get_available_balance(base)}")

            if exchange == "ascend_ex":
                self.logger().info(f"did_fail_order: try to cancel order: {event.order_id}")
                in_flight_order = xemm_proposal.market_info_maker.market.in_flight_orders.get(event.order_id, None)
                if in_flight_order:
                    exchange_order_id = in_flight_order.exchange_order_id
                    trading_pair = in_flight_order.trading_pair
                    safe_ensure_future(self.cancel_order_by_exchange_order_id(market_info=xemm_proposal.market_info_maker, exchange_order_id=exchange_order_id, trading_pair=trading_pair))

        # handle if taker order
        if self._taker_order_dict.has(event.order_id):
            xemm_trade = self._taker_order_dict.get(event.order_id)
            xemm_trade.set_failed()
            exchange = xemm_trade.market_info_taker.market.name
            market_info_taker = xemm_trade.market_info_taker
            self.time_out_dict[exchange] = False
            base, quote = market_info_taker.base_asset, market_info_taker.quote_asset
            safe_ensure_future(market_info_taker.market._update_balances())
            self.logger().info(f"did_fail_order: {exchange}, quote: {market_info_taker.market.get_available_balance(quote)}, base: {market_info_taker.market.get_available_balance(base)}")

        self.log_inflight_orders()
        self.order_latency.pop(event.order_id, None)

    def log_inflight_orders(self):
        # log all orders to debug
        self.logger().info("in_flight_orders:")

        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            for order in market_info.market.in_flight_orders.values():
                order_id = order.client_order_id
                amount = order.amount
                side = "BUY" if order.trade_type == TradeType.BUY else "SELL"
                current_state = order.current_state
                self.logger().info(f"{exchange}, side: {side}, order_id: {order_id}, amount: {amount}, current_state: {current_state}")

    def did_fill_order(self, event: OrderFilledEvent):
        """
        Method called when the connector notifies an order has been filled
        """
        self.logger().info(f"did_fill_order: order_id: received: event")
        asyncio.create_task(self.async_did_fill_order(event))

    async def async_did_fill_order(self, event: OrderFilledEvent):

        # handle if maker order
        if self._maker_order_dict.has(event.order_id):
            xemm_arb_proposal = self._maker_order_dict.get(event.order_id)
            xemm_arb_proposal.update_order_fill_maker(event)
            self.logger().info(f"Filled maker {event.trade_type} order for {event.amount} with price: {event.price} exchange: {xemm_arb_proposal.market_info_maker.market.name}")
            # place hedge
            if xemm_arb_proposal.process_hedge_by_public_trade:
                self.logger().info(f"did_fill_order: order id: {event.order_id} is all ready processed by a public event")
            else:
                xemm_arb_proposal.process_hedge_by_fill_event = True
                self.logger().info(f"did_fill_order: place hedge for: {event}")
                res = self.get_xemm_trade(xemm_arb_proposal, event.amount)

                if res:
                    xemm_trade, hedge_amount = res
                    await self.execute_hedge(xemm_trade, hedge_amount)

                if self.report_to_dbs:
                    safe_ensure_future(
                        self.reporter.send_order_fill_data(xemm_arb_proposal.market_info_maker, event, self.last_recorded_mid_prices.get(xemm_arb_proposal.market_info_maker.market.name)))

        # handle if taker order
        if self._taker_order_dict.has(event.order_id):
            xemm_trades = self._taker_order_dict.get(event.order_id)
            xemm_trades.executed_amount_taker += event.amount
            # report to dbs
            if self.report_to_dbs:
                safe_ensure_future(self.reporter.send_order_fill_data(xemm_trades.market_info_maker, event, self.last_recorded_mid_prices.get(xemm_trades.market_info_maker.market.name)))

    def handle_order_complete_event(self, event):
        # handle if maker order
        if self._maker_order_dict.has(event.order_id):
            self._maker_order_dict.delete(event.order_id)

        # handle if taker order
        if self._taker_order_dict.has(event.order_id):
            xemm_trade = self._taker_order_dict.get(event.order_id)
            xemm_trade.set_completed()
            self._taker_order_dict.delete(event.order_id)
            exchange = xemm_trade.market_info_taker.market.name
            self.time_out_dict[exchange] = False

        self.order_latency.pop(event.order_id, None)
        self.ids_to_cancel.discard(event.order_id)
        self.log_inflight_orders()

    def did_complete_sell_order(self, event: SellOrderCompletedEvent):
        """
        Method called when the connector notifies a sell order has been completed (fully filled)
        """
        self.handle_order_complete_event(event)

    def did_complete_buy_order(self, event: BuyOrderCompletedEvent):
        """
        Method called when the connector notifies a buy order has been completed (fully filled)
        """
        self.handle_order_complete_event(event)

    def did_cancel_order(self, event: OrderCancelledEvent):
        """
        Method called when the connector notifies an order has been cancelled
        """
        if event.order_id in self.order_cancel_events:
            self.order_cancel_events[event.order_id].set()

        # handle if maker order
        if self._maker_order_dict.has(event.order_id):
            xemm_arb_proposal = self._maker_order_dict.get(event.order_id)
            exchange = xemm_arb_proposal.market_info_maker.market.name
            self._global_unhedged_position += xemm_arb_proposal.hanging_amount if xemm_arb_proposal.is_buy_maker else -xemm_arb_proposal.hanging_amount
            self._maker_order_dict.delete(event.order_id)
            if self.is_perp[exchange]:
                safe_ensure_future(xemm_arb_proposal.market_info_maker.market._update_balances())

        # handle if taker order
        if self._taker_order_dict.has(event.order_id):
            self.logger().info(f"did_cancel_order: taker order {event.order_id} has been cancelled! ")

            for market_info in self.all_trading_pair_tuples:
                try:
                    exchange = market_info.market.name
                    # Attempt to access the in-flight order using the order ID
                    order = market_info.market.in_flight_orders[event.order_id]
                    xemm_trade = self._taker_order_dict.get(event.order_id)
                    self._taker_order_dict.delete(event.order_id)
                    amount_left = order.amount - order.executed_amount_base
                    pair = order.trading_pair
                    self.logger().info(f"did_cancel_order: place left over order: exchange: {exchange} part: {pair} amount_left: {amount_left} ")
                    loop = asyncio.get_running_loop()
                    loop.create_task(self.execute_hedge(xemm_trade, amount_left))
                    break

                except KeyError:
                    continue

        self.order_latency.pop(event.order_id, None)

    def did_complete_funding_payment(self, funding_payment_completed_event: FundingPaymentCompletedEvent):
        """
        Based on the funding payment event received, check if one of the active arbitrages matches to add the event
        to the list.
        """
        self.logger().info(f"did_complete_funding_payment: {funding_payment_completed_event}")

    def subscribe_to_order_book_update_event(self):
        for market_info in self._all_trading_pair_tuples_list:
            market = market_info.market
            market_name = market.name
            for symbol, order_book in market.order_books.items():
                key = (market_name, symbol)
                market_specific_handler = partial(self._process_order_book_update, market_name, symbol)
                self.order_book_diff_events[key] = SourceInfoEventForwarder(market_specific_handler)
                order_book.add_listener(OrderBookEvent.DiffEvent, self.order_book_diff_events[key])

    def _process_order_book_update(self, market_name: str, symbol: str, event_tag: int, connector: ConnectorBase, update_id: int):
        self.main_tick_logic()
        self.ob_update_event_timestamps.append(time.perf_counter() * 1e3)

    def add_listeners(self):
        """
        Add listeners.
        """
        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            trading_pair = market_info.trading_pair
            key = (exchange, trading_pair)
            market_specific_handler = lambda event_tag, market, event, exch=exchange, sym=trading_pair: \
                self.process_public_trade(exch, sym, event_tag, market, event)
            self._order_book_trade_events[key] = SourceInfoEventForwarder(market_specific_handler)
            market_info.market.get_order_book(trading_pair).add_listener(OrderBookEvent.TradeEvent, self._order_book_trade_events[key])

    def process_public_trade(self, market_name: str, symbol: str, event_tag: int, market: ConnectorBase, event: OrderBookTradeEvent):
        """
        Process public trade events.

        Args:
            market_name (str): The market name.
            symbol (str): The trading pair.
            event_tag (int): The event tag.
            market (ConnectorBase): The market where the event occurred.
            event (OrderBookTradeEvent): The trade event details.
        """

        is_buy_public = True if event.type == TradeType.BUY else False
        price_public = event.price
        exchange_name_public = market_name

        # check if this public trade filled our order
        for ex, order, client_order_id in self.active_maker_limit_orders:
            exchange_name = ex.display_name
            is_buy_private = order.is_buy
            price_private = order.price
            order_status = order.status
            # todo: we should check queue position at the price level
            if exchange_name_public == exchange_name and is_buy_private != is_buy_public and (
                    (price_public < price_private and is_buy_private) or (price_public > price_private and not is_buy_private)):
                self.logger().info(f"early fill detection: {client_order_id} order_status: {order_status} ")
                xemm_arb_proposal = self._maker_order_dict.get(client_order_id)

                if not xemm_arb_proposal.process_hedge_by_fill_event and not xemm_arb_proposal.process_hedge_by_public_trade:
                    xemm_arb_proposal.process_hedge_by_public_trade = True
                    # hedge trade
                    self.logger().info(f"place hedge for {client_order_id} based on public data")
                    res = self.get_xemm_trade(xemm_arb_proposal, xemm_arb_proposal.amount)
                    if res:
                        xemm_trade, hedge_amount = res
                        loop = asyncio.get_running_loop()
                        loop.create_task(self.execute_hedge(xemm_trade, hedge_amount))

    def stop(self, clock: Clock):
        """
        Without this functionality, the network iterator will continue running forever after stopping the strategy
        """
        # set main_tick_logic_running to true to stop calculating new orders when we update on orderbook diff
        self.main_tick_logic_running = True

        super().stop(clock)

        if self.report_to_dbs:
            self.reporter.stop()

        if self.data_collector:
            self.data_collector.stop()

        # todo fix freeze on stop when using auto_buy_sell_inventory and no inventory bought
        if not self.auto_buy_sell_inventory_base_amount == Decimal(0):
            self.auto_buy_sell_inventory.sell_inventory()

            while not self.auto_buy_sell_inventory.is_finished:
                self.logger().info("Waiting for auto_buy_sell_inventory to finish...")
                time.sleep(5)

        for market_info in self._all_trading_pair_tuples_list:
            exchange = market_info.market.name
            pair = market_info.trading_pair
            # stop indicator threads
            try:
                self.volatility_indicator[exchange][pair].stop()
            except KeyError:
                pass
