from numba import boolean, float64
from numba.experimental import jitclass

# 1. Define the data layout
spec = [
    ("buy_volume", float64),
    ("sell_volume", float64),
]


@jitclass(spec)
class TradeFlowImbalance:
    def __init__(self):
        # start from zero
        self.buy_volume = 0.0
        self.sell_volume = 0.0

    def add_trade(self, amount: float64, is_buy: boolean):
        """
        Add a trade to the accumulator.

        Parameters
        ----------
        is_buy : bool
            True for a buy trade, False for a sell trade.
        amount : float
            The trade size.
        """
        if is_buy:
            self.buy_volume += amount
        else:
            self.sell_volume += amount

    def calculate(self) -> float64:
        """
        Compute the Trade Flow Imbalance:
            (buy_volume - sell_volume) / (buy_volume + sell_volume)
        or 0 if no volume, then reset both to zero.

        Returns
        -------
        tfi : float
            The current TFI, in [-1,1].
        """
        total = self.buy_volume + self.sell_volume
        if total != 0.0:
            tfi = (self.buy_volume - self.sell_volume) / total
        else:
            tfi = 0.0

        # reset for next accumulation
        self.buy_volume = 0.0
        self.sell_volume = 0.0
        return tfi
