import numpy as np
from numba import types
from numba.experimental import jitclass

spec_hawkes = [
    ("kappa", types.float64),
    ("alpha", types.float64),
    ("state", types.float64),
]


@jitclass(spec_hawkes)
class HawkesFilter:
    def __init__(self, kappa: float, init_value: float):
        self.kappa = kappa
        self.alpha = np.exp(-kappa)
        self.state = init_value

    def update(self, x: float) -> float:
        if self.state != self.state:  # nan check
            self.state = x
        else:
            self.state = self.state * self.alpha + x
        return self.kappa * self.state
