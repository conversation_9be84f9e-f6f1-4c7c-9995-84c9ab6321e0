from hummingbot.strategy.__utils__.ring_buffer import Ring<PERSON>uffer


class VWAPCalculator:
    """
    O(1) rolling VWAP on the last N trades
    """
    __slots__ = ("window", "_pv_buf", "_vol_buf", "_sum_pv", "_sum_vol")

    def __init__(self, window: int):
        self.window = window
        self._pv_buf = RingBuffer(window)
        self._vol_buf = RingBuffer(window)

    def update(self, price: float, volume: float) -> float:
        """
        Add one tick and return the rolling VWAP.
        Runs in O(1). Avoids division by zero when total volume is zero.
        """
        # Add new price and volume to buffers
        self._pv_buf.add_value(price)
        self._vol_buf.add_value(volume)

        pv_array = self._pv_buf.get_as_numpy_array()
        volume_array = self._vol_buf.get_as_numpy_array()

        # Compute total volume
        total_volume = volume_array.sum()

        if total_volume == 0:
            return float('nan')

        vwap = pv_array.dot(volume_array) / total_volume
        return vwap

    @property
    def vwap(self) -> float:
        """
        Current VWAP without adding a new tick.
        """
        vwap = self._pv_buf.get_as_numpy_array().dot(self._vol_buf.get_as_numpy_array()) / self._vol_buf.get_as_numpy_array().sum()
        return vwap
