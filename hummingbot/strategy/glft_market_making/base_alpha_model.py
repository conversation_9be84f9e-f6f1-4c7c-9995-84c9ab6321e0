import logging
from typing import List

import numpy as np

from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.core.event.event_forwarder import SourceInfoEventForwarder
from hummingbot.core.event.events import OrderBookEvent, OrderBookTradeEvent
from hummingbot.strategy.glft_market_making.utils import MarketTradingPairTupleUUID

hb_logger = None


class BaseAlphaModel:

    @classmethod
    def logger(cls):
        global hb_logger
        if hb_logger is None:
            hb_logger = logging.getLogger(__name__)
        return hb_logger

    def __init__(self, market_infos: List[MarketTradingPairTupleUUID], main_loop_interval_s: float = 1.0):
        self._market_infos = market_infos
        self._market_info_id_to_market_info = {i: market_info for i, market_info in enumerate(market_infos)}
        self._main_loop_interval_s = main_loop_interval_s
        self._signal = 0
        self._order_book_trade_events = {}
        self._running = False
        # self._main_loop_task = safe_ensure_future(self.run_main_loop())
        self.add_listeners()

    @property
    def signal(self):
        return self._signal

    @property
    def place_buy_orders(self):
        raise NotImplementedError("place_buy_orders must be implemented by the subclass.")

    @property
    def place_sell_orders(self):
        raise NotImplementedError("place_sell_orders must be implemented by the subclass.")

    @property
    def global_best_bid(self):
        best_price = np.inf
        for market_info in self._market_infos:
            order_book_iterator = market_info.market.get_order_book(market_info.trading_pair).bid_entries()
            first_ob_entry_price = next(order_book_iterator, None).price
            if first_ob_entry_price < best_price:
                best_price = first_ob_entry_price
        return best_price

    @property
    def global_best_ask(self):
        best_price = - np.inf
        for market_info in self._market_infos:
            order_book_iterator = market_info.market.get_order_book(market_info.trading_pair).ask_entries()
            first_ob_entry_price = next(order_book_iterator, None).price
            if first_ob_entry_price > best_price:
                best_price = first_ob_entry_price
        return best_price

    def get_global_mid_price(self):
        """
        Get the global mid price of the markets.
        """
        return self.calculate_global_fair_price()

    def calculate_global_fair_price(self):
        """
        Calculate the global fair price.
        """
        raise NotImplementedError("calculate_global_fair_price must be implemented by the subclass.")

    def calculate_signal(self):
        """
        Calculate the signal.
        """
        raise NotImplementedError("calculate_signal must be implemented by the subclass.")

    def process_public_trade(self, market_info_id: int,  market_name: str, symbol: str, event_tag: int, market: ConnectorBase, event: OrderBookTradeEvent):
        """
        Process public trade events.

        Args:
            market_info_id (int): The market info ID.
            market_name (str): The market name.
            symbol (str): The trading pair.
            event_tag (int): The event tag.
            market (ConnectorBase): The market where the event occurred.
            event (OrderBookTradeEvent): The trade event details.
        """
        pass

    def add_listeners(self):
        """
        Add listeners.
        """
        for i, market_info in self._market_info_id_to_market_info.items():
            exchange, trading_pair = market_info.market.name, market_info.trading_pair
            key = (i, exchange, trading_pair)
            market_specific_handler = lambda event_tag, market, event, market_info_id=i, exch=exchange, sym=trading_pair: \
                self.process_public_trade(market_info_id, exch, sym, event_tag, market, event)
            self._order_book_trade_events[key] = SourceInfoEventForwarder(market_specific_handler)
            market_info.market.get_order_book(trading_pair).add_listener(OrderBookEvent.TradeEvent, self._order_book_trade_events[key])

    def main_function(self):
        """
        Placeholder for the main function of the indicator.
        """
        raise NotImplementedError("main_function must be implemented by the subclass.")

    def stop(self):
        """
        Signal the main loop to stop and wait for the thread to finish.
        """
        self._running = False
        try:
            self._main_loop_task.cancel()
        except AttributeError:
            pass

    def on_stop(self):
        """
        Placeholder for the on stop function of the indicator.
        """
        raise NotImplementedError("on_stop must be implemented by the subclass.")

    def __del__(self):
        self.stop()
