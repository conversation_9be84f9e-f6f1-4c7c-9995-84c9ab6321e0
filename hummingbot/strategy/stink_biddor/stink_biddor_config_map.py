from decimal import Decimal
from typing import Optional

from hummingbot.client.config.config_validators import validate_decimal, validate_exchange, validate_market_trading_pair
from hummingbot.client.config.config_var import ConfigVar
from hummingbot.client.settings import AllConnectorSettings, required_exchanges


def exchange_on_validated(value: str) -> None:
    required_exchanges.add(value)


def maker_trading_pair_prompt():
    exchange = stink_biddor_config_map.get("exchange").value
    example = AllConnectorSettings.get_example_pairs().get(exchange)
    return "Enter the token trading pair you would like to trade on %s%s >>> " \
           % (exchange, f" (e.g. {example})" if example else "")


def validate_exchange_trading_pair(value: str) -> Optional[str]:
    exchange = stink_biddor_config_map.get("exchange").value
    return validate_market_trading_pair(exchange, value)


stink_biddor_config_map = {
    "strategy": ConfigVar(
        key="strategy",
        prompt="",
        default="stink_biddor"),
    "exchange":
        ConfigVar(key="exchange",
                  prompt="Enter your maker spot connector >>> ",
                  validator=validate_exchange,
                  on_validated=exchange_on_validated,
                  prompt_on_new=True),
    "market":
        ConfigVar(key="market",
                  prompt=maker_trading_pair_prompt,
                  validator=validate_exchange_trading_pair,
                  prompt_on_new=True),
    "order_level_map": ConfigVar(
        key="order_level_map",
        prompt="",
        prompt_on_new=True,
        type_str="decimal"),
    "volatility_to_spread_multiplier": ConfigVar(
        key="volatility_to_spread_multiplier",
        prompt="volatility_to_spread_multiplier ? >>> ",
        prompt_on_new=True,
        default=Decimal("1"),
        validator=lambda v: validate_decimal(v),
        type_str="decimal"),

}
