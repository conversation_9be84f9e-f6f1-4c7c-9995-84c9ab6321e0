import logging
import time
from collections import deque
from decimal import Decimal
from typing import Any, Dict, List

import numpy as np
import pandas as pd

from hummingbot.connector.derivative.position import Position
from hummingbot.core.clock import Clock
from hummingbot.core.data_type.common import OrderType, PositionAction, TradeType
from hummingbot.core.data_type.in_flight_order import OrderState
from hummingbot.core.data_type.order_candidate import OrderCandidate, PerpetualOrderCandidate
from hummingbot.core.event.events import (
    BuyOrderCompletedEvent,
    BuyOrderCreatedEvent,
    MarketOrderFailureEvent,
    OrderCancelledEvent,
    OrderFilledEvent,
    SellOrderCompletedEvent,
    SellOrderCreatedEvent,
)
from hummingbot.strategy.Indicators.volatility_indicator import VolatilityIndicator
from hummingbot.strategy.market_trading_pair_tuple import MarketTradingPairTuple
from hummingbot.strategy.stink_biddor.utils import HedgeOrder
from hummingbot.strategy.strategy_py_base import StrategyPyBase

NaN = float("nan")
s_decimal_zero = Decimal(0)
s_decimal_neg_one = Decimal(-1)
s_decimal_nan = Decimal("NaN")
s_float_nan = float("nan")
s_decimal_1 = Decimal("1")
s_decimal_0 = Decimal("0")
hb_logger = None


class StinkBiddorStrategy(StrategyPyBase):
    @classmethod
    def logger(cls):
        global hb_logger
        if hb_logger is None:
            hb_logger = logging.getLogger(__name__)
        return hb_logger

    def init_params(self,
                    market_info: MarketTradingPairTuple,
                    order_level_map: Dict,
                    volatility_to_spread_multiplier: Decimal = Decimal("1")):

        self._market_info = market_info

        # Initialize variables
        self._all_markets_ready = False
        self._status_ready = False
        self._order_level_map = order_level_map
        self._order_id_to_order_level_key_map = {}
        self._is_perp = True if self._market_info.market.name.endswith("perpetual_testnet") or self._market_info.market.name.endswith("perpetual") else False
        self._min_notional_size_dict = {}
        self._min_os_size_dict = {}
        self._order_id_creation_timestamp_map = {}
        self._latency_roundtrip = deque(maxlen=10)  # store the last 10 latencies
        self._hedge_order_id_to_HedgeOrder = {}
        self._unhedged_position = Decimal(0)
        self._volatility_indicator = None
        self._volatility_to_spread_multiplier = volatility_to_spread_multiplier

        for key, order_level in self._order_level_map.items():
            order_level["order_amount_quote"] = Decimal(order_level["order_amount_quote"])
            order_level["bps_from_mid"] = Decimal(order_level["bps_from_mid"])
            order_level["take_profit_bps"] = Decimal(order_level["take_profit_bps"])
            # order_level["tp_resting_time_seconds"] = Decimal(order_level["tp_resting_time_seconds"])
            order_level["order_refresh_threshold_bps"] = Decimal(order_level["order_refresh_threshold_bps"])
            order_level["buy_order_id"] = None
            order_level["sell_order_id"] = None

        self.add_markets([self._market_info.market])

    @property
    def active_limit_orders(self):
        return [(ex, order, order.client_order_id) for ex, order in self._sb_order_tracker.active_limit_orders]

    @property
    def active_positions(self) -> Dict[str, Position]:
        return self._market_info.market.account_positions

    def is_above_min_order_size(self, exchange_name, amount, price):
        return True if self._min_notional_size_dict[exchange_name] * Decimal(1.1) < (amount * price) and self._min_os_size_dict[exchange_name] < amount else False

    def set_variables(self):
        self.logger().info("Setting variables")

        self._min_notional_size_dict[self._market_info.market.name] = self._market_info.market.trading_rules[self._market_info.trading_pair].min_notional_size
        self._min_os_size_dict[self._market_info.market.name] = self._market_info.market.trading_rules[self._market_info.trading_pair].min_order_size
        self._volatility_indicator = VolatilityIndicator(self._market_info, 1)
        self._status_ready = True

    def adjusted_vol(self, vol):
        """vol needs to be in decimal. not percent"""
        if vol <= Decimal(0.0005):
            return Decimal(0)
        else:
            adjusted_vol = (vol - Decimal(0.0005))
            return adjusted_vol * self._volatility_to_spread_multiplier

    def get_reservation_price(self):

        if self._unhedged_position == s_decimal_0:
            return self._market_info.market.get_mid_price(self._market_info.trading_pair)

        best_bid = self._market_info.market.get_price(self._market_info.trading_pair, False)
        best_ask = self._market_info.market.get_price(self._market_info.trading_pair, True)
        mid_price = (best_ask + best_bid) / Decimal("2")
        vol = self._volatility_indicator.current_value_fraction  # vol in decimal
        order_size_base = self._order_level_map["order_level_1"]["order_amount_quote"] / mid_price
        risk_aversion_y = s_decimal_1

        res_price = mid_price - ((self._unhedged_position / order_size_base) * vol * risk_aversion_y)
        res_price = min(res_price, best_ask)
        res_price = max(res_price, best_bid)

        return res_price

    def main_tick_logic(self):

        # check orders
        self.check_orders()

        # create orders
        self.place_orders()

        # check if we need to cancel and replace hedge orders
        self.check_hedge_orders()

    def check_orders(self):

        reservation_price = self.get_reservation_price()
        vol = self._volatility_indicator.current_value_fraction  # vol in decimal
        adjusted_vol = self.adjusted_vol(vol)

        for order_level in self._order_level_map.values():

            for side in ["buy", "sell"]:
                order_id = order_level["buy_order_id"] if side == "buy" else order_level["sell_order_id"]

                if (order_id is not None and side == "buy") or (order_id is not None and side == "sell"):
                    inflight_order = self._market_info.market.in_flight_orders[order_id]

                    if inflight_order.current_state == OrderState.PENDING_CANCEL:
                        continue

                    actual_price = inflight_order.price
                    cancel = True

                    if side == "buy":
                        soll_price = reservation_price * (s_decimal_1 - order_level["bps_from_mid"] / Decimal("10000") - adjusted_vol)

                    else:
                        soll_price = reservation_price * (s_decimal_1 + order_level["bps_from_mid"] / Decimal("10000") + adjusted_vol)

                    if abs(actual_price - soll_price) <= (order_level["order_refresh_threshold_bps"] / Decimal("10000")) * soll_price:
                        cancel = False

                    if cancel:
                        self.cancel_order(self._market_info, order_id)

    def check_hedge_orders(self):

        # check if we should cancel hedge orders and place hedge chase instead
        for order_id, hedge_order in self._hedge_order_id_to_HedgeOrder.items():
            inflight_order = self._market_info.market.in_flight_orders[order_id]

            if not hedge_order.is_chase_order and time.time() > hedge_order.cancellation_timestamp and inflight_order.current_state != OrderState.PENDING_CANCEL:
                self.cancel_order(self._market_info, order_id)
            elif hedge_order.is_chase_order and inflight_order.current_state != OrderState.PENDING_CANCEL:
                current_price = hedge_order.price
                new_optimal_price = self.get_best_price(self._market_info, hedge_order.trade_type == TradeType.BUY)
                if current_price != new_optimal_price:
                    self.cancel_order(self._market_info, order_id)

    def place_orders(self):
        """
        Place orders based on the order level map
        """

        reservation_price = self.get_reservation_price()
        vol = self._volatility_indicator.current_value_fraction  # vol in decimal
        adjusted_vol = self.adjusted_vol(vol)

        for level_key, order_level in self._order_level_map.items():
            for side in ["buy", "sell"]:

                if (order_level["buy_order_id"] is None and side == "buy") or (order_level["sell_order_id"] is None and side == "sell"):

                    if side == "buy":
                        order_price = reservation_price * (s_decimal_1 - order_level["bps_from_mid"] / Decimal("10000") - adjusted_vol)
                    else:
                        order_price = reservation_price * (s_decimal_1 + order_level["bps_from_mid"] / Decimal("10000") + adjusted_vol)

                    if self._is_perp:
                        order = PerpetualOrderCandidate(
                            trading_pair=self._market_info.trading_pair,
                            is_maker=True,
                            order_type=OrderType.LIMIT,
                            order_side=TradeType.BUY if side == "buy" else TradeType.SELL,
                            amount=Decimal(order_level["order_amount_quote"]) / order_price,
                            price=order_price,
                        )
                    else:
                        order = OrderCandidate(
                            trading_pair=self._market_info.trading_pair,
                            is_maker=True,
                            order_type=OrderType.LIMIT,
                            order_side=TradeType.BUY if side == "buy" else TradeType.SELL,
                            amount=Decimal(order_level["order_amount_quote"]) / order_price,
                            price=order_price, )

                    order_adjusted = self._market_info.market.budget_checker.adjust_candidate(order, all_or_none=False)
                    is_above_min_os = self.is_above_min_order_size(self._market_info.market.name, order_adjusted.amount, order_adjusted.price)

                    # place order
                    if is_above_min_os and not order_adjusted.amount == Decimal(0):

                        if side == "buy":
                            order_id = self.buy_with_specific_market(self._market_info, order_adjusted.amount, order_adjusted.order_type, order_adjusted.price,
                                                                     position_action=PositionAction.OPEN)
                            order_level["buy_order_id"] = order_id if order_id else None
                            self._order_id_to_order_level_key_map[order_id] = level_key
                        else:
                            order_id = self.sell_with_specific_market(self._market_info, order_adjusted.amount, order_adjusted.order_type, order_adjusted.price,
                                                                      position_action=PositionAction.OPEN)
                            order_level["sell_order_id"] = order_id if order_id else None
                            self._order_id_to_order_level_key_map[order_id] = level_key

                        self._order_id_creation_timestamp_map[order_id] = time.perf_counter()

    def tick(self, timestamp: float):

        if not self._all_markets_ready:
            self._all_markets_ready = all([market.ready for market in self.active_markets])
            if not self._all_markets_ready:
                self.logger().warning("Markets are not ready. No market making trades are permitted.")
                return
            else:
                self.logger().info("Markets are ready.")

        if not self._status_ready:
            self.set_variables()

        self.main_tick_logic()

    # events
    def did_create_buy_order(self, event: BuyOrderCreatedEvent):
        """
        Method called when the connector notifies an order has been created
        """
        # self.did_create_order(event)
        self.calculate_latency(event)
        return

    def did_create_sell_order(self, event: SellOrderCreatedEvent):
        """
        Method called when the connector notifies an order has been created
        """
        # self.did_create_order(event)
        self.calculate_latency(event)
        return

    def did_fail_order(self, event: MarketOrderFailureEvent):
        """
        Method called when the connector notifies an order has failed
        """
        self.clear_order_id_from_order_level_map(event.order_id)
        hedge_order = self._hedge_order_id_to_HedgeOrder.pop(event.order_id, None)
        if hedge_order:
            self._unhedged_position = self._unhedged_position - hedge_order.amount if hedge_order.trade_type == TradeType.BUY else self._unhedged_position + hedge_order.amount
            self.logger().info(f"Order {event.order_id} failed. self._unhedged_position: {self._unhedged_position}")

        self.calculate_latency(event)

    def did_fill_order(self, event: OrderFilledEvent):
        """
        Method called when the connector notifies an order has been filled
        """

        # place on the other side if it's a maker order
        order_level_key = self._order_id_to_order_level_key_map.get(event.order_id, None)
        if order_level_key:
            amount = event.amount
            price = event.price
            is_buy = True if event.trade_type == TradeType.BUY else False
            vol = self._volatility_indicator.current_value_fraction  # vol in decimal
            adjusted_vol = self.adjusted_vol(vol)
            take_profit_bps = self._order_level_map[order_level_key]["take_profit_bps"]
            tp_resting_time_seconds = self._order_level_map[order_level_key]["tp_resting_time_seconds"]
            tp_price = price * (s_decimal_1 + take_profit_bps / Decimal("10000") + adjusted_vol) if is_buy else price * (s_decimal_1 - take_profit_bps / Decimal("10000") - adjusted_vol)
            amount_to_hedge = amount + self._unhedged_position if is_buy else amount - self._unhedged_position
            hedge_is_buy = not is_buy
            if amount_to_hedge < s_decimal_0:  # change side if amount is negative
                hedge_is_buy = not hedge_is_buy
                amount_to_hedge = - amount_to_hedge

            if self.is_above_min_order_size(self._market_info.market.name, amount_to_hedge, tp_price):
                if hedge_is_buy:
                    order_id = self.buy_with_specific_market(self._market_info, amount_to_hedge, OrderType.LIMIT, tp_price, position_action=PositionAction.CLOSE)
                    self.logger().info(f"Placed take profit buy order for {amount_to_hedge} at {tp_price} id {order_id}")
                else:
                    order_id = self.sell_with_specific_market(self._market_info, amount_to_hedge, OrderType.LIMIT, tp_price, position_action=PositionAction.CLOSE)
                    self.logger().info(f"Placed take profit sell order for {amount_to_hedge} at {tp_price} id {order_id}")

                hedge_order = HedgeOrder(trade_type=TradeType.BUY if hedge_is_buy else TradeType.SELL,
                                         amount=amount_to_hedge,
                                         price=tp_price,
                                         order_id=order_id,
                                         creation_timestamp=time.time(),
                                         cancellation_timestamp=time.time() + tp_resting_time_seconds,
                                         is_chase_order=False)
                self._hedge_order_id_to_HedgeOrder[order_id] = hedge_order
            else:
                self._unhedged_position = amount_to_hedge if not hedge_is_buy else - amount_to_hedge
                self.logger().info(f"Amount {amount_to_hedge} is below min order size. Not hedging. unhedged position is {self._unhedged_position}")

        # notify the app
        self.notify_hb_app_with_timestamp(f"Order {event.order_id} filled. Amount: {event.amount} Price: {event.price} Trade type: {event.trade_type}")

    def did_complete_sell_order(self, event: SellOrderCompletedEvent):
        """
        Method called when the connector notifies a sell order has been completed (fully filled)
        """
        self.clear_order_id_from_order_level_map(event.order_id)
        self._hedge_order_id_to_HedgeOrder.pop(event.order_id, None)

    def did_complete_buy_order(self, event: BuyOrderCompletedEvent):
        """
        Method called when the connector notifies a buy order has been completed (fully filled)
        """
        self.clear_order_id_from_order_level_map(event.order_id)
        self._hedge_order_id_to_HedgeOrder.pop(event.order_id, None)

    def did_cancel_order(self, event: OrderCancelledEvent):
        """
        Method called when the connector notifies an order has been cancelled
        """
        self.clear_order_id_from_order_level_map(event.order_id)

        if event.order_id in self._hedge_order_id_to_HedgeOrder:
            hedge_order = self._hedge_order_id_to_HedgeOrder.pop(event.order_id)
            is_buy = True if hedge_order.trade_type == TradeType.BUY else False

            # place chase order
            new_optimal_price = self.get_best_price(self._market_info, is_buy)
            if is_buy:
                order_id = self.buy_with_specific_market(self._market_info, hedge_order.amount, OrderType.LIMIT, new_optimal_price, position_action=PositionAction.CLOSE)
                self.logger().info(f"Placed chase buy order for {hedge_order.amount} at {new_optimal_price} id {order_id}")
            else:
                order_id = self.sell_with_specific_market(self._market_info, hedge_order.amount, OrderType.LIMIT, new_optimal_price, position_action=PositionAction.CLOSE)
                self.logger().info(f"Placed chase sell order for {hedge_order.amount} at {new_optimal_price} id {order_id}")

            new_hedge_order = HedgeOrder(trade_type=hedge_order.trade_type,
                                         amount=hedge_order.amount,
                                         price=new_optimal_price,
                                         order_id=order_id,
                                         creation_timestamp=time.time(),
                                         cancellation_timestamp=None,
                                         is_chase_order=True)
            self._hedge_order_id_to_HedgeOrder[order_id] = new_hedge_order

    def clear_order_id_from_order_level_map(self, order_id):

        order_level_key = self._order_id_to_order_level_key_map.pop(order_id, None)
        if order_level_key is not None:
            if self._order_level_map[order_level_key]["buy_order_id"] == order_id:
                self._order_level_map[order_level_key]["buy_order_id"] = None

            if self._order_level_map[order_level_key]["sell_order_id"] == order_id:
                self._order_level_map[order_level_key]["sell_order_id"] = None

    def calculate_latency(self, event):

        start_time = self._order_id_creation_timestamp_map.pop(event.order_id, None)
        if start_time:
            latency = (time.perf_counter() - start_time) * 1000
            self._latency_roundtrip.append(latency)

    def get_best_price(self, market_info, is_buy):
        if is_buy:
            best_bid = market_info.market.get_price(market_info.trading_pair, False)
            price = self.optimize_order_placement(best_bid, is_buy, market_info, optimize_order=True)
        else:
            best_ask = market_info.market.get_price(market_info.trading_pair, True)
            price = self.optimize_order_placement(best_ask, is_buy, market_info, optimize_order=True)

        return price

    def optimize_order_placement(self, price, is_bid, market_info, optimize_order=True):

        def amount_sub_my_orders_and_ignore_small_orders(ob_entry, amount_to_ignore_quote=20):
            amount = ob_entry.amount
            if ob_entry.price in my_limit_orders:
                amount = ob_entry.amount - float(my_limit_orders[ob_entry.price])
            return amount if amount > (amount_to_ignore_quote / ob_entry.price) else 0

        my_limit_orders = {}
        min_price_step = market_info.market.trading_rules[market_info.trading_pair].min_price_increment
        for (ex, order, order_id) in self.active_limit_orders:
            if ex.display_name == market_info.market.name:
                my_limit_orders[float(round(order.price / min_price_step) * min_price_step)] = order.quantity

        # place order just if front of another, don't quote higher than best bid ask
        if is_bid:
            order_book_iterator = market_info.market.get_order_book(market_info.trading_pair).bid_entries()
            first_ob_entry = next(order_book_iterator, None)
            if price >= first_ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(first_ob_entry) != 0:
                return market_info.market.quantize_order_price(market_info.trading_pair, Decimal(first_ob_entry.price))

            if optimize_order:
                for ob_entry in order_book_iterator:
                    if price == ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return Decimal(ob_entry.price)
                    if price > ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return market_info.market.quantize_order_price(market_info.trading_pair, Decimal(ob_entry.price) + min_price_step)

        else:
            order_book_iterator = market_info.market.get_order_book(market_info.trading_pair).ask_entries()
            first_ob_entry = next(order_book_iterator, None)
            if price <= first_ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(first_ob_entry) != 0:
                return market_info.market.quantize_order_price(market_info.trading_pair, Decimal(first_ob_entry.price))

            if optimize_order:
                for ob_entry in order_book_iterator:
                    if price == ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return Decimal(ob_entry.price)
                    if price < ob_entry.price and amount_sub_my_orders_and_ignore_small_orders(ob_entry) != 0:
                        return market_info.market.quantize_order_price(market_info.trading_pair, Decimal(ob_entry.price) - min_price_step)

        return market_info.market.quantize_order_price(market_info.trading_pair, price)

    def format_status(self) -> str:
        """
        Returns status of the current strategy on user balances and current active orders. This function is called
        when status command is issued. Override this function to create custom status display output.
        """
        if not self._status_ready:
            return "Market connectors are not ready."
        lines = []
        warning_lines = []

        exchange_stats_df = self.get_exchange_stats_df()
        lines.extend(["", "  Exchange stats:"] + ["    " + line for line in exchange_stats_df.to_string(index=False).split("\n")])

        try:
            df = self.get_active_maker_trades_df()
            lines.extend(["", "  Maker Orders:"] + ["    " + line for line in df.to_string(index=False).split("\n")])
        except ValueError:
            lines.extend(["", "  No active maker orders."])

        balance_df = self.get_balance_df()
        lines.extend(["", "  Balances:"] + ["    " + line for line in balance_df.to_string(index=False).split("\n")])

        if len(warning_lines) > 0:
            lines.extend(["", "*** WARNINGS ***"] + warning_lines)
        return "\n".join(lines)

    def get_balance_df(self) -> pd.DataFrame:
        """
        Returns a data frame for all asset balances for displaying purpose.
        """
        columns: List[str] = ["Exchange", "Asset", "Total Balance", "Available Balance"]
        data: List[Any] = []

        data.append([self._market_info.market.display_name,
                     self._market_info.base_asset,
                     float(self._market_info.market.get_balance(self._market_info.base_asset)),
                     float(self._market_info.market.get_available_balance(self._market_info.base_asset))])
        data.append([self._market_info.market.display_name,
                     self._market_info.quote_asset,
                     float(self._market_info.market.get_balance(self._market_info.quote_asset)),
                     float(self._market_info.market.get_available_balance(self._market_info.quote_asset))])

        df = pd.DataFrame(data=data, columns=columns).replace(np.nan, '', regex=True)
        df.sort_values(by=["Exchange", "Asset"], inplace=True)
        return df

    def get_active_maker_trades_df(self) -> pd.DataFrame:
        """
        Return a data frame of all active orders for displaying purpose.
        """
        columns = ["Exchange", "Market", "Side", "Price", "Amount", "Age", "spread"]
        data = []
        for ex, order, order_id in self.active_limit_orders:
            age_txt = "n/a" if order.age() <= 0. else pd.Timestamp(order.age(), unit='s').strftime('%H:%M:%S')
            exchange_name = self._market_info.market.display_name
            mid_price = self._market_info.market.get_mid_price(order.trading_pair)
            spread = round(((abs(mid_price - order.price) / mid_price) * 100), 2)
            data.append([
                exchange_name,
                order.trading_pair,
                "buy" if order.is_buy else "sell",
                float(order.price),
                float(order.quantity),
                age_txt,
                f"{spread}%",
            ])
        if not data:
            raise ValueError
        df = pd.DataFrame(data=data, columns=columns)
        df.sort_values(by=["Exchange", "Market", "Side"], inplace=True)
        return df

    def get_exchange_stats_df(self):
        """
        Returns a data frame for all volatility for displaying purpose.
        """
        columns: List[str] = ["Ex", "vol", "spread", "mid", "res_price", "vol_add_spread", "rt_latency"]
        data: List[Any] = []

        pair = self._market_info.trading_pair
        vol_decimal = self._volatility_indicator.current_value_fraction
        vol_pct = self._volatility_indicator.current_value_pct
        vol_adjusted = self.adjusted_vol(vol_decimal)
        vol_additional_spread = vol_adjusted * Decimal(100)
        roundtrip_latency = sum(self._latency_roundtrip) / len(self._latency_roundtrip) if self._latency_roundtrip else 0
        spread = ((self._market_info.market.get_price(pair, True) - self._market_info.market.get_price(pair, False)) / self._market_info.market.get_price(pair, True)) * 100

        mid_price = self._market_info.market.get_mid_price(pair)
        res_price = self.get_reservation_price()
        data.append([self._market_info.market.display_name,
                     f"{round(vol_pct, 2)}%",
                     f"{round(spread, 2)}%",
                     f"{mid_price}",
                     f"{res_price}",
                     f"{round(vol_additional_spread, 2)}%",
                     f"{round(roundtrip_latency, 2)} ms"
                     ])

        df = pd.DataFrame(data=data, columns=columns).replace(np.nan, '', regex=True)
        return df

    def stop(self, clock: Clock):
        """
        Without this functionality, the network iterator will continue running forever after stopping the strategy
       """
        super().stop(clock)

        self._volatility_indicator.stop()
