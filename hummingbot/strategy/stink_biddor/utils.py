class HedgeOrder:
    def __init__(self, trade_type, amount, price, order_id, creation_timestamp, cancellation_timestamp, is_chase_order):
        self._trade_type = trade_type
        self._amount = amount
        self._price = price
        self._order_id = order_id
        self._creation_timestamp = creation_timestamp
        self._cancellation_timestamp = cancellation_timestamp
        self._is_chase_order = is_chase_order

    def __repr__(self):
        return (f"HedgeOrder(side={self._trade_type}, amount={self._amount}, price={self._price}, "
                f"order_id={self._order_id}, creation_timestamp={self._creation_timestamp}, "
                f"cancellation_timestamp={self._cancellation_timestamp}, is_chase_order={self._is_chase_order}")

    @property
    def trade_type(self):
        return self._trade_type

    @property
    def amount(self):
        return self._amount

    @property
    def price(self):
        return self._price

    @property
    def order_id(self):
        return self._order_id

    @property
    def creation_timestamp(self):
        return self._creation_timestamp

    @property
    def cancellation_timestamp(self):
        return self._cancellation_timestamp

    @property
    def is_chase_order(self):
        return self._is_chase_order
